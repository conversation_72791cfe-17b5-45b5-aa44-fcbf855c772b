@echo off
chcp 65001
echo 正在启动report服务...

cd %~dp0..
set JAVA_OPTS=-Dspring.profiles.active=dev -Dspring.cloud.nacos.discovery.namespace=tangpanhui -Xms512m -Xmx1024m -Dfile.encoding=UTF-8 -Dsun.stdout.encoding=UTF-8 -Dsun.stderr.encoding=UTF-8 -Dlog4j.charset=UTF-8 -Dlog4j2.charset=UTF-8

echo 正在重新编译report模块...
call mvn -f tx-module-report/pom.xml clean install -DskipTests
echo report模块编译完成

call mvn -f tx-module-report/tx-module-report-biz/pom.xml spring-boot:run -Dspring-boot.run.jvmArguments="%JAVA_OPTS%"

echo report服务已启动 