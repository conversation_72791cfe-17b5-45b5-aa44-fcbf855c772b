package cn.iocoder.yudao.module.infra.dal.mysql.demo;

import java.util.*;

import pojo.cn.tianxing.cloud.framework.common.PageResult;
import pojo.cn.tianxing.cloud.framework.common.PageParam;
import query.core.mybatis.cn.tianxing.cloud.framework.LambdaQueryWrapperX;
import mapper.core.mybatis.cn.tianxing.cloud.framework.BaseMapperX;
import cn.iocoder.yudao.module.infra.dal.dataobject.demo.InfraStudentContactDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 学生联系人 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InfraStudentContactMapper extends BaseMapperX<InfraStudentContactDO> {

    default List<InfraStudentContactDO> selectListByStudentId(Long studentId) {
        return selectList(InfraStudentContactDO::getStudentId, studentId);
    }

    default int deleteByStudentId(Long studentId) {
        return delete(InfraStudentContactDO::getStudentId, studentId);
    }

}