{"groups": [{"name": "yudao.codegen", "type": "cn.tianxing.cloud.module.infra.framework.codegen.config.CodegenProperties", "sourceType": "cn.tianxing.cloud.module.infra.framework.codegen.config.CodegenProperties"}], "properties": [{"name": "yudao.codegen.base-package", "type": "java.lang.String", "description": "生成的 Java 代码的基础包", "sourceType": "cn.tianxing.cloud.module.infra.framework.codegen.config.CodegenProperties"}, {"name": "yudao.codegen.db-schemas", "type": "java.util.Collection<java.lang.String>", "description": "数据库名数组", "sourceType": "cn.tianxing.cloud.module.infra.framework.codegen.config.CodegenProperties"}, {"name": "yudao.codegen.front-type", "type": "java.lang.Integer", "description": "代码生成的前端类型（默认） 枚举 {@link CodegenFrontTypeEnum#getType()}", "sourceType": "cn.tianxing.cloud.module.infra.framework.codegen.config.CodegenProperties"}, {"name": "yudao.codegen.unit-test-enable", "type": "java.lang.Bo<PERSON>an", "description": "是否生成单元测试", "sourceType": "cn.tianxing.cloud.module.infra.framework.codegen.config.CodegenProperties"}], "hints": []}