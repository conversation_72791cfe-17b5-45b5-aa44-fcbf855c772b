<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="4dfed190-b704-4c45-85a6-2e3e6d4754dd" name="Changes" comment="feat(crm): 增加还款明细的退款状态处理&#10;&#10;- 在 DebtRepaymentDetailsDO 中添加 refundStatus 字段，用于记录退款状态&#10;- 在 DebtRepaymentDetailsMapper 中添加根据订单号和退款状态查询的方法&#10;- 在 OrderServiceImpl 中增加对退款状态的处理逻辑&#10;- 优化 processOrder 方法，根据订单状态和退款状态决定是否处理订单" />
    <list id="3235ced2-636c-4f85-b819-5c667d19be56" name="忽略" comment="忽略">
      <change afterPath="$PROJECT_DIR$/tx-module-crm/tx-module-crm-biz/src/main/java/cn/tianxing/cloud/module/crm/ConfigDebug.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tx-cloud-base/tx-gateway/Dockerfile" beforeDir="false" afterPath="$PROJECT_DIR$/tx-cloud-base/tx-gateway/Dockerfile" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tx-cloud-base/tx-module-infra/tx-module-infra-biz/Dockerfile" beforeDir="false" afterPath="$PROJECT_DIR$/tx-cloud-base/tx-module-infra/tx-module-infra-biz/Dockerfile" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tx-cloud-base/tx-module-system/tx-module-system-biz/Dockerfile" beforeDir="false" afterPath="$PROJECT_DIR$/tx-cloud-base/tx-module-system/tx-module-system-biz/Dockerfile" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tx-module-crm/tx-module-crm-biz/Dockerfile" beforeDir="false" afterPath="$PROJECT_DIR$/tx-module-crm/tx-module-crm-biz/Dockerfile" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tx-module-member/tx-module-member-biz/Dockerfile" beforeDir="false" afterPath="$PROJECT_DIR$/tx-module-member/tx-module-member-biz/Dockerfile" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tx-module-report/tx-module-report-biz/Dockerfile" beforeDir="false" afterPath="$PROJECT_DIR$/tx-module-report/tx-module-report-biz/Dockerfile" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="CodeInsightWorkspaceSettings">
    <option name="optimizeImportsOnTheFly" value="true" />
  </component>
  <component name="CompilerWorkspaceConfiguration">
    <option name="MAKE_PROJECT_ON_SAVE" value="true" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <excluded-from-favorite>
      <branch-storage>
        <map>
          <entry type="REMOTE">
            <value>
              <list>
                <branch-info repo="$PROJECT_DIR$/tx-module-crm" source="origin/main" />
                <branch-info repo="$PROJECT_DIR$/tx-cloud-base" source="origin/main" />
                <branch-info repo="$PROJECT_DIR$/tx-module-member" source="origin/main" />
                <branch-info repo="$PROJECT_DIR$/tx-module-report" source="origin/main" />
              </list>
            </value>
          </entry>
        </map>
      </branch-storage>
    </excluded-from-favorite>
    <favorite-branches>
      <branch-storage>
        <map>
          <entry type="REMOTE">
            <value>
              <list>
                <branch-info repo="$PROJECT_DIR$/tx-cloud-base" source="origin/dev" />
              </list>
            </value>
          </entry>
        </map>
      </branch-storage>
    </favorite-branches>
    <branch-grouping />
    <option name="RECENT_COMMON_BRANCH" value="dev" />
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/tx-module-crm" />
    <option name="ROOT_SYNC" value="SYNC" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="jar://$MAVEN_REPOSITORY$/com/baomidou/mybatis-plus-core/3.5.9/mybatis-plus-core-3.5.9.jar!/com/baomidou/mybatisplus/core/handlers/MetaObjectHandler.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$PROJECT_DIR$/../../apache-maven-3.6.3/repository/com/baomidou/mybatis-plus-core/3.5.9/mybatis-plus-core-3.5.9.jar!/com/baomidou/mybatisplus/core/mapper/BaseMapper.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$PROJECT_DIR$/../../apache-maven-3.6.3/repository/com/baomidou/mybatis-plus-spring/3.5.9/mybatis-plus-spring-3.5.9.jar!/com/baomidou/mybatisplus/extension/service/IService.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$PROJECT_DIR$/../../apache-maven-3.6.3/repository/org/springframework/spring-aop/6.2.1/spring-aop-6.2.1.jar!/org/springframework/aop/support/AopUtils.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$PROJECT_DIR$/../../jdk-17/lib/src.zip!/java.base/java/util/Map.java" root0="SKIP_INSPECTION" />
  </component>
  <component name="JRebelWorkspace">
    <option name="jrebelEnabledAutocompile" value="true" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="E:\tph\apache-maven-3.6.3" />
        <option name="localRepository" value="E:\tph\apache-maven-3.6.3\repository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="E:\tph\apache-maven-3.6.3\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2yAD2QZMChRn4E3BxMW3VuHW35c" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="1" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Application.CaseEntrustManualServiceImpl.executor&quot;: &quot;Run&quot;,
    &quot;Application.ContractBasicInfoServiceImpl.executor&quot;: &quot;Run&quot;,
    &quot;Docker.Docker Image.executor&quot;: &quot;Run&quot;,
    &quot;Docker.cc.executor&quot;: &quot;Run&quot;,
    &quot;Docker.crm_dockerfile.executor&quot;: &quot;Run&quot;,
    &quot;Docker.docker-compose.yml.crm: Compose Deployment.executor&quot;: &quot;Run&quot;,
    &quot;Docker.docker-compose.yml.gateway: Compose Deployment.executor&quot;: &quot;Run&quot;,
    &quot;Docker.docker-compose.yml.infra: Compose Deployment.executor&quot;: &quot;Run&quot;,
    &quot;Docker.docker-compose.yml.member: Compose Deployment.executor&quot;: &quot;Run&quot;,
    &quot;Docker.docker-compose.yml.system: Compose Deployment.executor&quot;: &quot;Run&quot;,
    &quot;Docker.docker-compose.yml: Compose Deployment (1).executor&quot;: &quot;Run&quot;,
    &quot;Docker.docker-compose.yml: Compose Deployment.executor&quot;: &quot;Run&quot;,
    &quot;Docker.gateway_dockerfile.executor&quot;: &quot;Run&quot;,
    &quot;Docker.infra_dockerfile.executor&quot;: &quot;Run&quot;,
    &quot;Docker.member_container.executor&quot;: &quot;Run&quot;,
    &quot;Docker.member_dockerfile.executor&quot;: &quot;Run&quot;,
    &quot;Docker.system_dockerfile.executor&quot;: &quot;Run&quot;,
    &quot;Maven.tx-cloud-base [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.tx-cloud-base [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.tx-framework [clean,install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.tx-framework [clean,package,install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.tx-framework [clean,package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.tx-framework [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.tx-gateway [clean,package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.tx-module-crm [clean,install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.tx-module-crm [clean,package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.tx-module-crm [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.tx-module-crm [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.tx-module-crm-biz [clean,package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.tx-module-crm-biz [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.tx-module-infra [clean,install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.tx-module-infra [clean,package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.tx-module-infra [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.tx-module-member [clean,package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.tx-module-member [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.tx-module-member [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.tx-module-member-api [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.tx-module-member-biz [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.tx-module-member-biz [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.tx-module-report [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.tx-module-report-biz [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.tx-module-system [clean,package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.tx-module-system [install].executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager&quot;: &quot;true&quot;,
    &quot;Spring Boot.CrmServerApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.GatewayServerApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.InfraServerApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.MemberServerApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.ReportServerApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.SystemServerApplication.executor&quot;: &quot;Run&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;dev&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/tph/xm/back-end2/tx-module-member/tx-module-member-biz/src/main/java/cn/tianxing/cloud/module/member/controller/admin/card/vo&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Modules&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.33908045&quot;,
    &quot;run.configurations.included.in.services&quot;: &quot;true&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;spring.configuration.checksum&quot;: &quot;69b56de310157c383c4b3bba0c70c240&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql&quot;
    ]
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RebelAgentSelection">
    <selection>jr</selection>
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\tph\xm\back-end2\tx-module-member\tx-module-member-biz\src\main\java\cn\tianxing\cloud\module\member\controller\admin\card\vo" />
      <recent name="E:\tph\xm\back-end2\.cursor\rules" />
      <recent name="E:\tph\xm\back-end2\tx-cloud-base\tx-module-system\tx-module-system-biz\src\main\resources\dal\mapper" />
      <recent name="E:\tph\xm\back-end2\tx-module-member\tx-module-member-biz\src\main\resources\mapper\user" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="E:\tph\xm\back-end2\tx-cloud-base\tx-module-system\tx-module-system-biz\src\main\resources\mapper" />
      <recent name="E:\tph\xm\back-end2\tx-cloud-base\tx-module-system\tx-module-system-biz\src\main\resources\images\test" />
      <recent name="E:\tph\xm\back-end2\tx-cloud-base\tx-module-system\tx-module-system-biz\src\main\resources\images" />
      <recent name="E:\tph\xm\back-end2\tx-module-member\tx-module-member-biz\src\main\resources\mapper\card" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="cn.tianxing.cloud.module.crm.controller.admin.test" />
    </key>
  </component>
  <component name="RestServicesNavigator">
    <treeState />
  </component>
  <component name="RunAnythingCache">
    <option name="myCommands">
      <command value="mvn clean" />
      <command value="mvn install" />
    </option>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="KtorApplicationConfigurationType" />
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Docker.docker-compose.yml.infra: Compose Deployment">
    <configuration name="CrmServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="tx-module-crm-biz" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="cn.tianxing.cloud.module.crm.CrmServerApplication" />
      <option name="VM_PARAMETERS" value="-Dspring.cloud.nacos.discovery.namespace=tangpanhui -Dspring.profiles.active=dev" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="GatewayServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="tx-gateway" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="cn.tianxing.cloud.gateway.GatewayServerApplication" />
      <option name="VM_PARAMETERS" value="-Dspring.cloud.nacos.discovery.namespace=tangpanhui -Dspring.profiles.active=dev" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="InfraServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="tx-module-infra-biz" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="cn.tianxing.cloud.module.infra.InfraServerApplication" />
      <option name="VM_PARAMETERS" value="-Dspring.cloud.nacos.discovery.namespace=tangpanhui -Dspring.profiles.active=dev" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="MemberServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="tx-module-member-biz" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="cn.tianxing.cloud.module.member.MemberServerApplication" />
      <option name="VM_PARAMETERS" value="-Dspring.cloud.nacos.discovery.namespace=tangpanhui -Dspring.profiles.active=dev" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ReportServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="tx-module-report-biz" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="cn.tianxing.cloud.module.report.ReportServerApplication" />
      <option name="VM_PARAMETERS" value="-Dspring.cloud.nacos.discovery.namespace=tangpanhui -Dspring.profiles.active=dev" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SystemServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="tx-module-system-biz" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="cn.tianxing.cloud.module.system.SystemServerApplication" />
      <option name="VM_PARAMETERS" value="-Dspring.cloud.nacos.discovery.namespace=tangpanhui -Dspring.profiles.active=dev" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="docker-deploy" factoryName="docker-compose.yml" temporary="true">
      <deployment type="docker-compose.yml">
        <settings />
      </deployment>
      <method v="2" />
    </configuration>
    <configuration default="true" type="docker-deploy" factoryName="docker-image" temporary="true">
      <deployment type="docker-image">
        <settings />
      </deployment>
      <method v="2" />
    </configuration>
    <configuration name="crm_dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="imageTag" value="crm_image_1.0" />
          <option name="containerName" value="crm_container" />
          <option name="portBindings">
            <list>
              <DockerPortBindingImpl>
                <option name="containerPort" value="48089" />
                <option name="hostIp" value="**************" />
                <option name="hostPort" value="48089" />
              </DockerPortBindingImpl>
            </list>
          </option>
          <option name="sourceFilePath" value="tx-module-crm/tx-module-crm-biz/Dockerfile" />
        </settings>
      </deployment>
      <method v="2">
        <option name="Maven.BeforeRunTask" enabled="true" file="$PROJECT_DIR$/tx-cloud-base/tx-framework/pom.xml" goal="clean install" />
        <option name="Maven.BeforeRunTask" enabled="true" file="$PROJECT_DIR$/tx-module-crm/pom.xml" goal="clean package" />
      </method>
    </configuration>
    <configuration name="docker-compose.yml: Compose Deployment (1)" type="docker-deploy" factoryName="docker-compose.yml" temporary="true" server-name="Docker">
      <deployment type="docker-compose.yml">
        <settings>
          <option name="sourceFilePath" value="docker-compose.yml" />
        </settings>
      </deployment>
      <method v="2" />
    </configuration>
    <configuration name="docker-compose.yml: Compose Deployment" type="docker-deploy" factoryName="docker-compose.yml" temporary="true" server-name="Docker2">
      <deployment type="docker-compose.yml">
        <settings>
          <option name="sourceFilePath" value="docker-compose.yml" />
        </settings>
      </deployment>
      <method v="2" />
    </configuration>
    <configuration name="docker-compose.yml.crm: Compose Deployment" type="docker-deploy" factoryName="docker-compose.yml" temporary="true" server-name="Docker">
      <deployment type="docker-compose.yml">
        <settings>
          <option name="services">
            <list>
              <option value="crm" />
            </list>
          </option>
          <option name="sourceFilePath" value="docker-compose.yml" />
        </settings>
      </deployment>
      <method v="2" />
    </configuration>
    <configuration name="docker-compose.yml.gateway: Compose Deployment" type="docker-deploy" factoryName="docker-compose.yml" temporary="true" server-name="Docker">
      <deployment type="docker-compose.yml">
        <settings>
          <option name="services">
            <list>
              <option value="gateway" />
            </list>
          </option>
          <option name="sourceFilePath" value="docker-compose.yml" />
        </settings>
      </deployment>
      <method v="2" />
    </configuration>
    <configuration name="docker-compose.yml.infra: Compose Deployment" type="docker-deploy" factoryName="docker-compose.yml" temporary="true" server-name="Docker">
      <deployment type="docker-compose.yml">
        <settings>
          <option name="services">
            <list>
              <option value="infra" />
            </list>
          </option>
          <option name="sourceFilePath" value="docker-compose.yml" />
        </settings>
      </deployment>
      <method v="2" />
    </configuration>
    <configuration name="gateway_dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="imageTag" value="gateway_image_1.0" />
          <option name="containerName" value="gateway_container" />
          <option name="sourceFilePath" value="tx-cloud-base/tx-gateway/Dockerfile" />
        </settings>
      </deployment>
      <method v="2">
        <option name="Maven.BeforeRunTask" enabled="true" file="$PROJECT_DIR$/tx-cloud-base/tx-framework/pom.xml" goal="clean install" />
        <option name="Maven.BeforeRunTask" enabled="true" file="$PROJECT_DIR$/tx-cloud-base/tx-gateway/pom.xml" goal="clean package" />
      </method>
    </configuration>
    <configuration name="infra_dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="imageTag" value="infra_image_1.0" />
          <option name="containerName" value="infra_container" />
          <option name="sourceFilePath" value="tx-cloud-base/tx-module-infra/tx-module-infra-biz/Dockerfile" />
        </settings>
      </deployment>
      <method v="2">
        <option name="Maven.BeforeRunTask" enabled="true" file="$PROJECT_DIR$/tx-cloud-base/tx-framework/pom.xml" goal="clean install" />
        <option name="Maven.BeforeRunTask" enabled="true" file="$PROJECT_DIR$/tx-cloud-base/tx-module-infra/pom.xml" goal="clean package" />
      </method>
    </configuration>
    <configuration name="member_dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="imageTag" value="member_image_1.0" />
          <option name="containerName" value="member_container" />
          <option name="portBindings">
            <list>
              <DockerPortBindingImpl>
                <option name="containerPort" value="48087" />
                <option name="hostIp" value="**************" />
                <option name="hostPort" value="48087" />
              </DockerPortBindingImpl>
            </list>
          </option>
          <option name="sourceFilePath" value="tx-module-member/tx-module-member-biz/Dockerfile" />
        </settings>
      </deployment>
      <method v="2">
        <option name="Maven.BeforeRunTask" enabled="true" file="$PROJECT_DIR$/tx-cloud-base/tx-framework/pom.xml" goal="clean install" />
        <option name="Maven.BeforeRunTask" enabled="true" file="$PROJECT_DIR$/tx-module-crm/pom.xml" goal="clean install" />
        <option name="Maven.BeforeRunTask" enabled="true" file="$PROJECT_DIR$/tx-module-member/pom.xml" goal="clean package" />
      </method>
    </configuration>
    <configuration name="system_dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="imageTag" value="system_image_1.0" />
          <option name="containerName" value="system_container" />
          <option name="sourceFilePath" value="tx-cloud-base/tx-module-system/tx-module-system-biz/Dockerfile" />
        </settings>
      </deployment>
      <method v="2">
        <option name="Maven.BeforeRunTask" enabled="true" file="$PROJECT_DIR$/tx-cloud-base/tx-framework/pom.xml" goal="clean install" />
        <option name="Maven.BeforeRunTask" enabled="true" file="$PROJECT_DIR$/tx-cloud-base/tx-module-system/pom.xml" goal="clean package" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Docker.crm_dockerfile" />
      <item itemvalue="Docker.gateway_dockerfile" />
      <item itemvalue="Docker.infra_dockerfile" />
      <item itemvalue="Docker.member_dockerfile" />
      <item itemvalue="Docker.system_dockerfile" />
      <item itemvalue="Docker.docker-compose.yml.infra: Compose Deployment" />
      <item itemvalue="Docker.docker-compose.yml: Compose Deployment (1)" />
      <item itemvalue="Docker.docker-compose.yml: Compose Deployment" />
      <item itemvalue="Docker.docker-compose.yml.crm: Compose Deployment" />
      <item itemvalue="Docker.docker-compose.yml.gateway: Compose Deployment" />
      <item itemvalue="Spring Boot.CrmServerApplication" />
      <item itemvalue="Spring Boot.GatewayServerApplication" />
      <item itemvalue="Spring Boot.InfraServerApplication" />
      <item itemvalue="Spring Boot.MemberServerApplication" />
      <item itemvalue="Spring Boot.ReportServerApplication" />
      <item itemvalue="Spring Boot.SystemServerApplication" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Docker.docker-compose.yml.infra: Compose Deployment" />
        <item itemvalue="Docker.docker-compose.yml.crm: Compose Deployment" />
        <item itemvalue="Docker.docker-compose.yml: Compose Deployment (1)" />
        <item itemvalue="Docker.docker-compose.yml.gateway: Compose Deployment" />
        <item itemvalue="Docker.docker-compose.yml: Compose Deployment" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="StructureViewState">
    <option name="selectedTab" value="Logical" />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="4dfed190-b704-4c45-85a6-2e3e6d4754dd" name="Changes" comment="" />
      <changelist id="3235ced2-636c-4f85-b819-5c667d19be56" name="忽略" comment="" />
      <created>1749268276574</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749268276574</updated>
      <workItem from="1749268277732" duration="927000" />
      <workItem from="1749269577019" duration="10424000" />
      <workItem from="1749431067288" duration="18883000" />
      <workItem from="1749517555569" duration="6550000" />
      <workItem from="1749541427966" duration="8517000" />
      <workItem from="1749604646046" duration="28778000" />
      <workItem from="1749690577470" duration="20269000" />
      <workItem from="1749777128577" duration="23494000" />
      <workItem from="1749863374926" duration="19116000" />
      <workItem from="1750036182882" duration="19304000" />
      <workItem from="1750123105085" duration="18748000" />
      <workItem from="1750208668071" duration="18419000" />
      <workItem from="1750295192726" duration="4811000" />
      <workItem from="1750302019202" duration="14650000" />
      <workItem from="1750381511823" duration="19230000" />
      <workItem from="1750468112239" duration="9423000" />
      <workItem from="1750490404158" duration="4634000" />
      <workItem from="1750641277036" duration="22398000" />
      <workItem from="1750727301960" duration="13565000" />
      <workItem from="1750751433359" duration="6136000" />
      <workItem from="1750813459889" duration="17158000" />
      <workItem from="1750900306515" duration="19112000" />
      <workItem from="1750986262106" duration="725000" />
      <workItem from="1750987002014" duration="5590000" />
      <workItem from="1750992950492" duration="2000" />
      <workItem from="1750993092827" duration="1063000" />
      <workItem from="1750995459744" duration="1839000" />
      <workItem from="1751003231563" duration="2270000" />
      <workItem from="1751005518136" duration="69000" />
      <workItem from="1751005608776" duration="1420000" />
      <workItem from="1751007047259" duration="10566000" />
      <workItem from="1751246906795" duration="222000" />
      <workItem from="1751247136766" duration="283000" />
      <workItem from="1751247431775" duration="1259000" />
      <workItem from="1751248717081" duration="1039000" />
      <workItem from="1751249773806" duration="21000" />
      <workItem from="1751249802811" duration="20733000" />
      <workItem from="1751331706478" duration="22438000" />
      <workItem from="1751418783517" duration="18434000" />
      <workItem from="1751504958202" duration="14870000" />
      <workItem from="1751591572186" duration="507000" />
      <workItem from="1751592092533" duration="15081000" />
      <workItem from="1751677742554" duration="9134000" />
      <workItem from="1751704156202" duration="550000" />
      <workItem from="1751704719464" duration="2804000" />
      <workItem from="1751850619822" duration="15353000" />
      <workItem from="1751877177678" duration="2671000" />
      <workItem from="1751880453281" duration="1776000" />
      <workItem from="1751936781516" duration="20509000" />
      <workItem from="1752023138993" duration="11052000" />
      <workItem from="1752042279332" duration="178000" />
      <workItem from="1752042464764" duration="9574000" />
      <workItem from="1752110447437" duration="10509000" />
      <workItem from="1752131825035" duration="430000" />
      <workItem from="1752132271019" duration="179000" />
      <workItem from="1752132465808" duration="331000" />
      <workItem from="1752132811455" duration="2251000" />
      <workItem from="1752135261541" duration="2787000" />
      <workItem from="1752197423531" duration="17605000" />
      <workItem from="1752224975151" duration="1211000" />
      <workItem from="1752455783306" duration="12624000" />
      <workItem from="1752541669674" duration="5317000" />
      <workItem from="1752549274175" duration="1127000" />
      <workItem from="1752565230201" duration="1375000" />
      <workItem from="1752567411425" duration="5009000" />
      <workItem from="1752629111885" duration="11828000" />
      <workItem from="1752654446829" duration="4301000" />
      <workItem from="1752714676235" duration="15053000" />
      <workItem from="1752801123658" duration="14345000" />
      <workItem from="1752825202647" duration="6726000" />
      <workItem from="1752887231243" duration="14541000" />
      <workItem from="1753060378254" duration="15637000" />
      <workItem from="1753089906922" duration="1790000" />
      <workItem from="1753146305996" duration="8427000" />
      <workItem from="1753164767270" duration="5366000" />
      <workItem from="1753174285321" duration="1736000" />
      <workItem from="1753232731994" duration="6186000" />
      <workItem from="1753319527017" duration="15719000" />
      <workItem from="1753405612345" duration="7205000" />
      <workItem from="1753413792198" duration="12718000" />
      <workItem from="1753435928236" duration="217000" />
      <workItem from="1753436156319" duration="1263000" />
      <workItem from="1753664914710" duration="4214000" />
    </task>
    <task id="LOCAL-00027" summary="每月化债情况">
      <created>1749794907157</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1749794907157</updated>
    </task>
    <task id="LOCAL-00028" summary="化债助力人">
      <created>1749800824474</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1749800824474</updated>
    </task>
    <task id="LOCAL-00029" summary="时间注解">
      <created>1749803250623</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1749803250623</updated>
    </task>
    <task id="LOCAL-00030" summary="流水管理">
      <created>1749804115099</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1749804115099</updated>
    </task>
    <task id="LOCAL-00031" summary="债务明细">
      <created>1749806388894</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1749806388894</updated>
    </task>
    <task id="LOCAL-00032" summary="月度化债明细bug">
      <created>1749806823484</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1749806823484</updated>
    </task>
    <task id="LOCAL-00033" summary="app端文件流">
      <created>1749865687171</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1749865687171</updated>
    </task>
    <task id="LOCAL-00034" summary="开卡银行补充字段">
      <created>1749867219071</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1749867219071</updated>
    </task>
    <task id="LOCAL-00035" summary="合同列表合同id">
      <created>1749869144975</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1749869144975</updated>
    </task>
    <task id="LOCAL-00036" summary="合同列表左连接">
      <created>1749884728217</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1749884728217</updated>
    </task>
    <task id="LOCAL-00037" summary="字段修复">
      <created>1749886336815</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1749886336815</updated>
    </task>
    <task id="LOCAL-00038" summary="字段修复">
      <created>1749888544610</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1749888544610</updated>
    </task>
    <task id="LOCAL-00039" summary="还款详情分页">
      <created>1749892677326</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1749892677326</updated>
    </task>
    <task id="LOCAL-00040" summary="合同列表字段补充">
      <created>1749893245656</created>
      <option name="number" value="00040" />
      <option name="presentableId" value="LOCAL-00040" />
      <option name="project" value="LOCAL" />
      <updated>1749893245656</updated>
    </task>
    <task id="LOCAL-00041" summary="区分app还款计划">
      <created>1749895080300</created>
      <option name="number" value="00041" />
      <option name="presentableId" value="LOCAL-00041" />
      <option name="project" value="LOCAL" />
      <updated>1749895080300</updated>
    </task>
    <task id="LOCAL-00042" summary="设置委案状态">
      <created>1750062116548</created>
      <option name="number" value="00042" />
      <option name="presentableId" value="LOCAL-00042" />
      <option name="project" value="LOCAL" />
      <updated>1750062116548</updated>
    </task>
    <task id="LOCAL-00043" summary="设置委案状态">
      <created>1750065169417</created>
      <option name="number" value="00043" />
      <option name="presentableId" value="LOCAL-00043" />
      <option name="project" value="LOCAL" />
      <updated>1750065169417</updated>
    </task>
    <task id="LOCAL-00044" summary="时间格式">
      <created>1750068099685</created>
      <option name="number" value="00044" />
      <option name="presentableId" value="LOCAL-00044" />
      <option name="project" value="LOCAL" />
      <updated>1750068099685</updated>
    </task>
    <task id="LOCAL-00045" summary="测试jekins">
      <created>1750213320853</created>
      <option name="number" value="00045" />
      <option name="presentableId" value="LOCAL-00045" />
      <option name="project" value="LOCAL" />
      <updated>1750213320853</updated>
    </task>
    <task id="LOCAL-00046" summary="测试jekins">
      <created>1750214818893</created>
      <option name="number" value="00046" />
      <option name="presentableId" value="LOCAL-00046" />
      <option name="project" value="LOCAL" />
      <updated>1750214818893</updated>
    </task>
    <task id="LOCAL-00047" summary="序列化解决">
      <created>1750229433342</created>
      <option name="number" value="00047" />
      <option name="presentableId" value="LOCAL-00047" />
      <option name="project" value="LOCAL" />
      <updated>1750229433342</updated>
    </task>
    <task id="LOCAL-00048" summary="序列化解决LocalDate">
      <created>1750229647482</created>
      <option name="number" value="00048" />
      <option name="presentableId" value="LOCAL-00048" />
      <option name="project" value="LOCAL" />
      <updated>1750229647482</updated>
    </task>
    <task id="LOCAL-00049" summary="序列化解决LocalDate">
      <created>1750230053509</created>
      <option name="number" value="00049" />
      <option name="presentableId" value="LOCAL-00049" />
      <option name="project" value="LOCAL" />
      <updated>1750230053509</updated>
    </task>
    <task id="LOCAL-00050" summary="测试系统时区">
      <created>1750233993977</created>
      <option name="number" value="00050" />
      <option name="presentableId" value="LOCAL-00050" />
      <option name="project" value="LOCAL" />
      <updated>1750233993978</updated>
    </task>
    <task id="LOCAL-00051" summary="机构配置">
      <created>1750382925658</created>
      <option name="number" value="00051" />
      <option name="presentableId" value="LOCAL-00051" />
      <option name="project" value="LOCAL" />
      <updated>1750382925658</updated>
    </task>
    <task id="LOCAL-00052" summary="获取可启用的会员列表">
      <created>1750401764968</created>
      <option name="number" value="00052" />
      <option name="presentableId" value="LOCAL-00052" />
      <option name="project" value="LOCAL" />
      <updated>1750401764968</updated>
    </task>
    <task id="LOCAL-00053" summary="去除机构配置不必要的接口">
      <created>1750408845236</created>
      <option name="number" value="00053" />
      <option name="presentableId" value="LOCAL-00053" />
      <option name="project" value="LOCAL" />
      <updated>1750408845236</updated>
    </task>
    <task id="LOCAL-00054" summary="修改机构分页接口请求方式">
      <created>1750410763094</created>
      <option name="number" value="00054" />
      <option name="presentableId" value="LOCAL-00054" />
      <option name="project" value="LOCAL" />
      <updated>1750410763094</updated>
    </task>
    <task id="LOCAL-00055" summary="设置system的mapper映射路径">
      <created>1750411363623</created>
      <option name="number" value="00055" />
      <option name="presentableId" value="LOCAL-00055" />
      <option name="project" value="LOCAL" />
      <updated>1750411363623</updated>
    </task>
    <task id="LOCAL-00056" summary="移动system模块的mapper.xml文件">
      <created>1750411785157</created>
      <option name="number" value="00056" />
      <option name="presentableId" value="LOCAL-00056" />
      <option name="project" value="LOCAL" />
      <updated>1750411785157</updated>
    </task>
    <task id="LOCAL-00057" summary="移动system模块的mapper.xml文件">
      <created>1750411830883</created>
      <option name="number" value="00057" />
      <option name="presentableId" value="LOCAL-00057" />
      <option name="project" value="LOCAL" />
      <updated>1750411830883</updated>
    </task>
    <task id="LOCAL-00058" summary="机构校验修复">
      <created>1750413146078</created>
      <option name="number" value="00058" />
      <option name="presentableId" value="LOCAL-00058" />
      <option name="project" value="LOCAL" />
      <updated>1750413146078</updated>
    </task>
    <task id="LOCAL-00059" summary="添加中征码字段">
      <created>1750469714898</created>
      <option name="number" value="00059" />
      <option name="presentableId" value="LOCAL-00059" />
      <option name="project" value="LOCAL" />
      <updated>1750469714898</updated>
    </task>
    <task id="LOCAL-00060" summary="添加邮箱等字段">
      <created>1750486757679</created>
      <option name="number" value="00060" />
      <option name="presentableId" value="LOCAL-00060" />
      <option name="project" value="LOCAL" />
      <updated>1750486757679</updated>
    </task>
    <task id="LOCAL-00061" summary="可选中机构列表">
      <created>1750490076694</created>
      <option name="number" value="00061" />
      <option name="presentableId" value="LOCAL-00061" />
      <option name="project" value="LOCAL" />
      <updated>1750490076694</updated>
    </task>
    <task id="LOCAL-00062" summary="虚拟手机号">
      <created>1750495846690</created>
      <option name="number" value="00062" />
      <option name="presentableId" value="LOCAL-00062" />
      <option name="project" value="LOCAL" />
      <updated>1750495846690</updated>
    </task>
    <task id="LOCAL-00063" summary="找回代码">
      <created>1750650455141</created>
      <option name="number" value="00063" />
      <option name="presentableId" value="LOCAL-00063" />
      <option name="project" value="LOCAL" />
      <updated>1750650455141</updated>
    </task>
    <task id="LOCAL-00064" summary="委案结构">
      <created>1750727635118</created>
      <option name="number" value="00064" />
      <option name="presentableId" value="LOCAL-00064" />
      <option name="project" value="LOCAL" />
      <updated>1750727635118</updated>
    </task>
    <task id="LOCAL-00065" summary="添加必要事务">
      <created>1750731838627</created>
      <option name="number" value="00065" />
      <option name="presentableId" value="LOCAL-00065" />
      <option name="project" value="LOCAL" />
      <updated>1750731838627</updated>
    </task>
    <task id="LOCAL-00066" summary="委案合同修复">
      <created>1750752703450</created>
      <option name="number" value="00066" />
      <option name="presentableId" value="LOCAL-00066" />
      <option name="project" value="LOCAL" />
      <updated>1750752703450</updated>
    </task>
    <task id="LOCAL-00067" summary="获取相关金额">
      <created>1750758523218</created>
      <option name="number" value="00067" />
      <option name="presentableId" value="LOCAL-00067" />
      <option name="project" value="LOCAL" />
      <updated>1750758523218</updated>
    </task>
    <task id="LOCAL-00068" summary="计算金额补充字段">
      <created>1750818780576</created>
      <option name="number" value="00068" />
      <option name="presentableId" value="LOCAL-00068" />
      <option name="project" value="LOCAL" />
      <updated>1750818780577</updated>
    </task>
    <task id="LOCAL-00069" summary="委案提交时绑定业务员id">
      <created>1750823488263</created>
      <option name="number" value="00069" />
      <option name="presentableId" value="LOCAL-00069" />
      <option name="project" value="LOCAL" />
      <updated>1750823488263</updated>
    </task>
    <task id="LOCAL-00070" summary="委案提交时绑定业务员id">
      <created>1750834774498</created>
      <option name="number" value="00070" />
      <option name="presentableId" value="LOCAL-00070" />
      <option name="project" value="LOCAL" />
      <updated>1750834774498</updated>
    </task>
    <task id="LOCAL-00071" summary="包装合同模版列表">
      <created>1750836108421</created>
      <option name="number" value="00071" />
      <option name="presentableId" value="LOCAL-00071" />
      <option name="project" value="LOCAL" />
      <updated>1750836108421</updated>
    </task>
    <task id="LOCAL-00072" summary="根据债权人id获取可用期数-app">
      <created>1750922944325</created>
      <option name="number" value="00072" />
      <option name="presentableId" value="LOCAL-00072" />
      <option name="project" value="LOCAL" />
      <updated>1750922944326</updated>
    </task>
    <task id="LOCAL-00073" summary="app端录入合同封装">
      <created>1750923953825</created>
      <option name="number" value="00073" />
      <option name="presentableId" value="LOCAL-00073" />
      <option name="project" value="LOCAL" />
      <updated>1750923953825</updated>
    </task>
    <task id="LOCAL-00074" summary="委案信息接口补充字段">
      <created>1750989303302</created>
      <option name="number" value="00074" />
      <option name="presentableId" value="LOCAL-00074" />
      <option name="project" value="LOCAL" />
      <updated>1750989303302</updated>
    </task>
    <task id="LOCAL-00075" summary="委案信息接口补充字段">
      <created>1750992228182</created>
      <option name="number" value="00075" />
      <option name="presentableId" value="LOCAL-00075" />
      <option name="project" value="LOCAL" />
      <updated>1750992228182</updated>
    </task>
    <option name="localTasksCounter" value="118" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnknownFeatures">
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-core" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.security:spring-security-core" />
    <option featureType="dependencySupport" implementationName="java:jakarta.validation:jakarta.validation-api" />
    <option featureType="dependencySupport" implementationName="java:io.projectreactor:reactor-core" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.data:spring-data-commons" />
    <option featureType="dependencySupport" implementationName="java:org.hibernate.validator:hibernate-validator" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.cloud:spring-cloud-context" />
    <option featureType="dependencySupport" implementationName="java:org.projectlombok:lombok" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-webmvc" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-websocket" />
    <option featureType="dependencySupport" implementationName="java:io.grpc:grpc-api" />
    <option featureType="dependencySupport" implementationName="java:io.reactivex.rxjava3:rxjava" />
    <option featureType="dependencySupport" implementationName="java:org.thymeleaf:thymeleaf" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-messaging" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.boot:spring-boot" />
    <option featureType="com.intellij.facetType" implementationName="JRebel" />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
        <option value="Space.CommitStatus" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="RECENT_FILTERS">
      <map>
        <entry key="User">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="*" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="dev" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
        <entry key="fd032fd7-95cb-406f-862e-60dea4323a89">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="structure">
                    <value>
                      <list>
                        <option value="dir:E:/tph/xm/back-end2/tx-cloud-base/tx-module-infra/tx-module-infra-biz" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="feat(member): 新增会员卡开通方式功能&#10;&#10;- 新增会员卡开通方式相关数据结构和接口定义&#10;- 实现会员卡开通方式的创建、更新、删除和查询功能&#10;- 重构会员卡相关数据结构，增加会员身份、会员费等字段&#10;- 优化会员卡相关接口，支持获取会员卡详情和列表" />
    <MESSAGE value="feat(member): 新增会员卡开通方式功能并重构会员卡相关代码&#10;&#10;- 新增会员卡开通方式相关实体、Mapper、Service及Controller&#10;- 重构会员卡基础信息，增加会员身份、会员费等字段&#10;- 更新会员卡Response VO，包含更多详细信息&#10;- 添加会员卡开通方式错误码" />
    <MESSAGE value="feat(member): 添加会员卡分页查询功能&#10;&#10;- 新增会员卡分页查询接口和相关VO类&#10;- 实现会员卡分页查询服务和 mapper 方法&#10;- 添加用户信息查询功能，获取更新人名称&#10;- 集成 UserIdApi 接口，用于查询用户信息" />
    <MESSAGE value="feat(member): 会员卡列表增加可升级别和可降级别名称&#10;&#10;- 在 MemberCardPageRespVO 中添加可升级别名称和可降级别名称字段&#10;- 在 MemberCardController 中调用新添加的 cardLevelConvert 方法处理级别转换- 在 MemberCardServiceImpl 中实现 cardLevelConvert 方法，用于设置可升级别和可降级别的名称" />
    <MESSAGE value="feat(member): 添加会员卡状态更新功能&#10;&#10;- 在 MemberCardController 中添加了 updateMemberCardStatus 方法，用于更新会员卡状态&#10;- 在 MemberCardService 接口中添加了 updateMemberCardStatus 方法的声明- 在 MemberCardServiceImpl 中实现了 updateMemberCardStatus 方法的逻辑&#10;- 新增了 MemberCardUpdateStatusReqVO 类作为更新状态的请求参数" />
    <MESSAGE value="feat(member): 获取除指定卡id外的会员卡列表接口&#10;&#10;- 新增 MemberCardListExcludeReqVO 和 MemberCardSimpleRespVO 类- 在 MemberCardController 中添加 getMemberCardListExclude 方法&#10;- 在 MemberCardService 接口中定义 getMemberCardListExclude 方法&#10;- 在 MemberCardServiceImpl 中实现 getMemberCardListExclude 方法&#10;- 在 MemberCardMapper 中添加 selectListExclude 方法- 在 MemberCardConvert 中添加 convertSimple 和 convertSimpleList 方法" />
    <MESSAGE value="refactor(member): 重构会员卡相关数据结构&#10;&#10;- 修改 MemberCardActivationBaseVO、MemberCardActivationDO、MemberCardDO 和相关 Mapper 文件&#10;- 重命名部分字段以更好地反映其含义&#10;- 移除冗余字段并添加新字段以适应业务需求&#10;- 优化数据结构以提高系统可维护性和扩展性" />
    <MESSAGE value="feat(member): 保存会员卡信息功能&#10;&#10;- 新增 MemberCardSaveVO 类用于保存会员卡信息- 在 MemberCardController 中添加 saveMemberCard 方法处理保存逻辑&#10;- 在 MemberCardService 接口中定义 saveMemberCard 方法&#10;- 实现 MemberCardServiceImpl 中的 saveMemberCard 方法，包括：&#10;  - 保存会员卡基本信息&#10;  - 处理会员卡权益关联&#10;  - 处理会员卡开通条件和结束条件&#10;- 在 MemberCardConvert 中添加 MemberCardSaveVO 到 MemberCardDO 的转换方法" />
    <MESSAGE value="refactor(member): 移除 MemberCardActivationDO 中的 status 字段&#10;&#10;移除了 MemberCardActivationDO 类中的 status 字段，该字段用于表示会员卡激活状态（0-启用，1-禁用）。此次修改可能是为了简化数据结构或使用其他方式来表示激活状态。" />
    <MESSAGE value="refactor(member): 优化会员卡列表查询接口&#10;&#10;- 移除 MemberCardListExcludeReqVO 类，简化请求参数&#10;- 修改 MemberCardController 中的 getMemberCardListExclude 方法，直接使用 Long 类型参数&#10;- 优化 MemberCardMapper 中的 selectListExclude 方法，增加非空判断" />
    <MESSAGE value="feat(member): 新增会员卡详情功能并优化相关逻辑&#10;&#10;- 新增会员卡详情接口和相关服务方法&#10;- 实现会员卡编码自动生成逻辑&#10;- 优化会员卡列表查询和保存逻辑&#10;- 新增通用的等级名称获取方法" />
    <MESSAGE value="feat(tx-module-member): 新增会员卡详情响应 VO 类&#10;&#10;新增 MemberCardDetailVO 类，用于管理后台展示会员卡详情信息。该类继承自 MemberCardDO，并添加了以下字段：&#10;- upgradableLevelName" />
    <MESSAGE value="feat(tx-module-member): 新增会员卡详情响应 VO 类&#10;&#10;新增 MemberCardDetailVO 类，用于管理后台展示会员卡详情信息。该类继承自 MemberCardDO，并添加了以下字段：&#10;- upgradableLevelName：可升级别名称&#10;- downgradableLevelName：可降级别名称&#10;- memberCardOpenList：开通条件列表&#10;- memberCardEndList：结束条件列表" />
    <MESSAGE value="feat(member): 增加会员卡绑定时更新会员数量功能- 在会员卡绑定服务中，增加更新会员卡会员数量的逻辑&#10;- 继承 IService 接口，使用 ServiceImpl 基类实现 MemberCardService&#10;-移除 MemberCardDetailVO 中的 LocalDateTime 导入" />
    <MESSAGE value="feat(member): 优化会员卡开通和结束条件处理逻辑&#10;&#10;-移除了 MemberCardActivationDO 类中的冗余注解&#10;- 更新了 type 字段的描述，明确其含义为 0 开通条件1 结束条件&#10;- 重构了 MemberCardServiceImpl 中的 save 和 getDetail 方法&#10;- 新增了按分组拆分和合并条件的逻辑，支持多条件录入- 优化了数据存储结构，提高了查询效率和准确性" />
    <MESSAGE value="refactor(member): 移除会员卡激活基础信息中的状态字段- 删除了 MemberCardActivationBaseVO 类中的 status 字段&#10;-该字段包括注解和相关文档也一并被移除" />
    <MESSAGE value="refactor(member): 重构会员卡权益相关功能&#10;&#10;- 将 CardBenefitQueryService 中的 getCardBenefitsByCardId 方法移动到 CardBenefitService 接口&#10;- 在 CardBenefitServiceImpl 中实现 getCardBenefitsByCardId 方法&#10;- 更新 AppMemberServiceController 和 MemberListController，使用新的 CardBenefitService 接口&#10;- 在 MemberCardDetailVO 中添加 cardBenefits 字段用于展示会员权益&#10;- 更新 MemberCardServiceImpl，在获取会员卡详情时查询并设置会员权益&#10;- 删除 CardBenefitQueryServiceImpl 中的冗余代码" />
    <MESSAGE value="refactor(member): 优化会员卡查询逻辑&#10;&#10;- 在 MemberCardMapper 中添加状态过滤，只查询启用的会员卡&#10;- 移除 MemberCardController 中的冗余代码" />
    <MESSAGE value="feat(member): 新增获取可升级和可绑定会员卡及权益列表功能&#10;&#10;- 新增 /card-benefits 接口，根据类型返回可升级或可绑定的会员卡及对应权益&#10;- 在 MemberCardDO 中添加 initialApplicationStatus 字段，用于表示是否可初次申请&#10;- 更新 MemberCardMapper 和相关 VO 类，支持 new 字段查询和展示&#10;- 优化 CardBenefitServiceImpl 中的 getCardBenefitsByCardId 方法，移除未使用的 BenefitService" />
    <MESSAGE value="refactor(member): 为会员卡信息添加 Swagger 注解&#10;&#10;- 在 MemberCardDO 类上添加了 @Schema 注解，用于描述会员卡信息&#10;- 在每个字段上添加了 @Schema 注解，用于描述字段的含义和示例值- 优化了代码结构，提高了代码的可读性和可维护性" />
    <MESSAGE value="refactor(mapper): 优化更新操作和查询逻辑&#10;&#10;- 注释掉 DefaultDBFieldHandler 中的冗余代码&#10;- 修改 updateFill 方法，简化更新人的设置逻辑&#10;- 在 OrderServiceImpl 中使用 LambdaQueryWrapper 改进查询效率&#10;-优化 caseEntrust 和 contractBasicInfo 的查询逻辑，获取最新记录" />
    <MESSAGE value="sec(order): 为处理订单接口添加 PermitAll 注解&#10;&#10;为 /processOrder 接口添加了 PermitAll 注解，允许所有用户（包括未登录用户）访问该接口" />
    <MESSAGE value="refactor(crm): 优化订单处理日志&#10;&#10;- 添加了日志记录，包括订单处理开始、会员用户不存在、委案信息未找到、合同信息未找到以及不处理的订单状态&#10;- 通过日志记录帮助排查订单处理过程中的问题" />
    <MESSAGE value="refactor(crm): 优化订单处理接口返回结构&#10;&#10;- 将 OrderController 中的 CommonResult&lt;Boolean&gt; 返回类型简化为 Boolean&#10;- 修改 OrderService 接口，将返回类型从 Boolean 改为 CommonResult&lt;Boolean&gt;&#10;- 更新 OrderServiceImpl 中的 processOrder 方法，适应新的返回类型&#10;- 优化错误处理逻辑，使用 CommonResult.error 包装错误信息" />
    <MESSAGE value="feat(crm): 增加还款明细的退款状态处理&#10;&#10;- 在 DebtRepaymentDetailsDO 中添加 refundStatus 字段，用于记录退款状态&#10;- 在 DebtRepaymentDetailsMapper 中添加根据订单号和退款状态查询的方法&#10;- 在 OrderServiceImpl 中增加对退款状态的处理逻辑&#10;- 优化 processOrder 方法，根据订单状态和退款状态决定是否处理订单" />
    <option name="LAST_COMMIT_MESSAGE" value="feat(crm): 增加还款明细的退款状态处理&#10;&#10;- 在 DebtRepaymentDetailsDO 中添加 refundStatus 字段，用于记录退款状态&#10;- 在 DebtRepaymentDetailsMapper 中添加根据订单号和退款状态查询的方法&#10;- 在 OrderServiceImpl 中增加对退款状态的处理逻辑&#10;- 优化 processOrder 方法，根据订单状态和退款状态决定是否处理订单" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/tx-module-member/tx-module-member-biz/src/main/java/cn/tianxing/cloud/module/member/service/user/impl/MemberUserDetailServiceImpl.java</url>
          <line>38</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/tx-module-member/tx-module-member-biz/src/main/java/cn/tianxing/cloud/module/member/service/user/impl/MemberUserDetailServiceImpl.java</url>
          <line>47</line>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/tx-module-member/tx-module-member-biz/src/main/java/cn/tianxing/cloud/module/member/service/card/impl/MemberCardServiceImpl.java</url>
          <line>161</line>
          <option name="timeStamp" value="4" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <pin-to-top-manager>
      <pinned-members>
        <PinnedItemInfo parentTag="cn.tianxing.cloud.module.crm.controller.admin.feedback.vo.FeedbackVO" memberName="attachment" />
      </pinned-members>
    </pin-to-top-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>