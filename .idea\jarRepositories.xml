<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="RemoteRepositoriesConfiguration">
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="https://repo.maven.apache.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="aliyunmaven" />
      <option name="name" value="aliyun" />
      <option name="url" value="https://maven.aliyun.com/repository/public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Maven Central repository" />
      <option name="url" value="https://repo1.maven.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="huaweicloud" />
      <option name="name" value="huawei" />
      <option name="url" value="https://mirrors.huaweicloud.com/repository/maven/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="spring-snapshots" />
      <option name="name" value="Spring Snapshots" />
      <option name="url" value="http://172.16.116.161:8081/repository/maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="nexus" />
      <option name="name" value="nexus" />
      <option name="url" value="http://172.16.116.161:8081/repository/maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="spring-milestones" />
      <option name="name" value="Spring Milestones" />
      <option name="url" value="http://172.16.116.161:8081/repository/maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="http://172.16.116.161:8081/repository/maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="aliyunmaven" />
      <option name="name" value="aliyun" />
      <option name="url" value="http://172.16.116.161:8081/repository/maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="spring-milestones" />
      <option name="name" value="Spring Milestones" />
      <option name="url" value="https://repo.spring.io/milestone" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="huaweicloud" />
      <option name="name" value="huawei" />
      <option name="url" value="http://172.16.116.161:8081/repository/maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="spring-snapshots" />
      <option name="name" value="Spring Snapshots" />
      <option name="url" value="https://repo.spring.io/snapshot" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jboss.community" />
      <option name="name" value="JBoss Community repository" />
      <option name="url" value="https://repository.jboss.org/nexus/content/repositories/public/" />
    </remote-repository>
  </component>
</project>