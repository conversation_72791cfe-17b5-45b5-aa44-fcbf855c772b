---
description: 代码规范
globs: 
alwaysApply: false
---
# 代码规范

## API设计
- RESTful API: 所有接口方法严格遵循 RESTful 风格
- 参数接收: 多参数时使用 POST + @RequestBody
- 单参数(简单类型): GET + @RequestParam 或路径参数 @PathVariable
- 统一响应: 所有接口返回 CommonResult<T> 统一格式

## 数据对象设计
- 数据流: 入参用 DTO，出参用 VO
- 文档完备: 所有 DO/DTO/VO 类字段必须有 @Schema 注解
- 继承规范: DO 类必须继承 BaseDO，ID 字段使用 @TableId(type = IdType.ASSIGN_ID)
- 命名规范: DO/DTO/VO 类命名必须以对应后缀结尾

## 代码结构
- Controller 精简: 方法体不超过 5 行，业务逻辑全部在 Service 层实现
- 分层清晰: Controller -> Service -> Mapper
- 异常处理: 使用全局异常处理，不在业务代码中捕获可预见异常
- 日志规范: 使用 SLF4J 注解 @Slf4j，关键操作必须记录日志

## 编码风格
- 命名规范: 类名用大驼峰，方法名和变量名用小驼峰
- 注释完备: 类、方法必须有 Javadoc 注释
- 代码格式: 遵循项目约定的代码格式化规则
- 常量使用: 避免魔法值，使用常量类或枚举