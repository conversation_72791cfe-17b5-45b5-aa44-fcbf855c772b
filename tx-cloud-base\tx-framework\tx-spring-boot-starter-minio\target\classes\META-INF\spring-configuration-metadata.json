{"groups": [{"name": "minio", "type": "cn.tianxing.cloud.framework.minio.config.MinioConfig", "sourceType": "cn.tianxing.cloud.framework.minio.config.MinioConfig"}], "properties": [{"name": "minio.access-key", "type": "java.lang.String", "sourceType": "cn.tianxing.cloud.framework.minio.config.MinioConfig"}, {"name": "minio.bucket-name", "type": "java.lang.String", "sourceType": "cn.tianxing.cloud.framework.minio.config.MinioConfig"}, {"name": "minio.endpoint", "type": "java.lang.String", "sourceType": "cn.tianxing.cloud.framework.minio.config.MinioConfig"}, {"name": "minio.secret-key", "type": "java.lang.String", "sourceType": "cn.tianxing.cloud.framework.minio.config.MinioConfig"}], "hints": []}