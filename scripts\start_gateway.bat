@echo off
chcp 65001
echo 正在启动gateway服务...

cd %~dp0..
set JAVA_OPTS=-Dspring.profiles.active=dev -Dspring.cloud.nacos.discovery.namespace=tangpanhui -Xms512m -Xmx1024m -Dfile.encoding=UTF-8 -Dsun.stdout.encoding=UTF-8 -Dsun.stderr.encoding=UTF-8 -Dlog4j.charset=UTF-8 -Dlog4j2.charset=UTF-8

echo 正在重新编译gateway模块...
call mvn -f tx-cloud-base/tx-gateway/pom.xml clean install -DskipTests
echo gateway模块编译完成

call mvn -f tx-cloud-base/tx-gateway/pom.xml spring-boot:run -Dspring-boot.run.jvmArguments="%JAVA_OPTS%"

echo gateway服务已启动 