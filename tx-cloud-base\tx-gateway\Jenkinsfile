pipeline {
    agent any
    parameters {
        choice(name: 'ENV', choices: ['dev', 'prod'], description: '选择部署环境')
    }
    tools {
        maven 'M3'
    }
    stages {
        stage('Clean') {
            steps {
                sh 'mvn clean install -U -DskipTests -Dmaven.test.skip=true -Dmaven.test.failure.ignore=true'
            }
        }
        stage('Build') {
            steps {
                sh 'mvn package -DskipTests -Dmaven.test.skip=true -Dmaven.test.failure.ignore=true'
            }
        }
        stage('Deploy') {
            steps {
                script {
                    def createTransfer = { String configName, String remoteDir ->
                        sshPublisherDesc(
                            configName: configName,
                            transfers: [
                                sshTransfer(
                                    sourceFiles: 'tx-gateway/target/tx-gateway.jar',
                                    removePrefix: 'tx-gateway/target',
                                    remoteDirectory: '/opt',
                                    execCommand: '''
                                        systemctl restart tx-gateway.service
                                    '''
                                )
                            ]
                        )
                    }

                    if (params.ENV == 'dev') {
                        sshPublisher publishers: [createTransfer('dev-gateway', '/opt')]
                    } else {
                        def prodNodes = [
                            [configName: 'prod-gateway1', remoteDir: '/opt'],
                            [configName: 'prod-gateway2', remoteDir: '/opt']
                        ]
                        sshPublisher publishers: prodNodes.collect {
                            createTransfer(it.configName, it.remoteDir)
                        }
                    }
                }
            }
        }
    }
}
