---
description: 建表sql语句
globs: 
alwaysApply: false
---
# 数据库表设计规则

## 表命名规范
1. 表名使用小写字母，单词间用下划线分隔
2. 表名应当使用名词，并且是单数形式
3. 表名应当能清晰表达表的用途和所存储的数据类型
4. 同一模块的表应当使用相同的前缀，例如：`system_`、`member_`、`contract_`、`case_`、`infra_`等
5. 模块前缀应与业务功能相关，例如：
   - `system_` - 系统管理相关表
   - `member_` - 会员管理相关表
   - `contract_` - 合同管理相关表
   - `case_` - 案件管理相关表
   - `infra_` - 基础设施相关表
   - `pay_` - 支付相关表

## 字段命名规范
1. 字段名使用小写字母，单词间用下划线分隔
2. 字段名应当简洁明了，能清晰表达字段用途
3. 外键字段通常以关联表名的单数形式加上`_id`后缀，例如：`user_id`、`dept_id`、`role_id`
4. 状态类字段使用`status`命名，枚举值类字段使用`type`命名
5. 布尔类型字段通常使用`is_`前缀或`deleted`、`valid`等表示状态的词，例如：`is_deleted`、`is_valid`、`deleted`
6. 对于表示时间点的字段，应使用`_time`或`_date`后缀，例如：`create_time`、`update_time`、`start_date`
7. 对于表示关联状态的字段，使用`_status`后缀，例如：`sign_status`、`approval_status`
8. 对于表示人员的字段，使用具体角色命名，例如：`creator`、`updater`、`approver`

## 字段类型选择规则
1. 主键：统一使用`bigint NOT NULL AUTO_INCREMENT`
2. 名称、标题类：
   - 短名称：`varchar(30)` 或 `varchar(50)`
   - 标题：`varchar(100)`
3. 描述、备注类：通常使用`varchar(255)`或`varchar(500)`
4. 枚举、状态类：
   - 简单状态：`tinyint` 
   - 字典值：`varchar(50)`
5. 金额类：使用`decimal(18,2)`保证精度，对于需要更高精度的可使用`decimal(18,4)`
6. 日期时间类：
   - 完整日期时间：`datetime`
   - 仅日期：`date`
7. 图片、文件URL：使用`varchar(255)`或`varchar(512)`
8. 大文本：使用`text`或`longtext`
9. 布尔类型：使用`tinyint(1)`或`bit(1)`
10. 证件号码、编码类：使用`varchar(32)`或`varchar(64)`
11. 联系方式：
    - 手机号：`varchar(11)`或`varchar(20)`
    - 电子邮箱：`varchar(50)`或`varchar(100)`
12. 地址信息：使用`varchar(200)`或`varchar(500)`

## 通用字段
每张表都应包含以下通用字段：
1. `id` - 主键，`bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID'`
2. `create_time` - 创建时间，`datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'`
3. `update_time` - 更新时间，`datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'`
4. `creator` - 创建人，`varchar(64)` 或 `varchar(255)` `DEFAULT '' COMMENT '创建人'`
5. `updater` - 更新人，`varchar(64)` 或 `varchar(255)` `DEFAULT '' COMMENT '更新人'`
6. `deleted` - 删除标记，两种常见格式：
   - `tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）'`
   - `bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除'`
7. `tenant_id` - 租户ID，`bigint NOT NULL DEFAULT '0' COMMENT '租户编号'` 或 `bigint DEFAULT NULL COMMENT '租户ID'`

## 索引设计规范
1. 主键索引：每个表必须有一个主键，通常为自增的`id`字段
2. 唯一索引：对于需要唯一性约束的字段，如编码、账号等，应添加唯一索引
   - 命名格式：`UNIQUE KEY idx_字段名`
3. 普通索引：对于经常作为查询条件的字段，应添加索引
   - 命名格式：`KEY idx_字段名`
4. 联合索引：对于经常一起作为查询条件的多个字段，应添加联合索引
   - 命名格式：`KEY idx_字段1_字段2`
5. 索引字段数量不宜过多，一般不超过5个
6. 索引命名应当遵循`idx_字段名`的格式

## 外键关系规范
1. **不使用物理外键约束**：在数据库层面不添加外键约束（FOREIGN KEY），而是在应用程序层面维护数据一致性
   - 原因：物理外键会带来性能开销，增加开发复杂度，并可能导致级联删除或更新的风险
2. 对于表示外键关系的字段，应在字段名和注释中明确说明关联关系
   - 例如：`user_id bigint NOT NULL COMMENT '用户ID，关联system_users表的id字段'`
3. 对于外键字段，应当添加索引以提高查询性能
   - 例如：`KEY idx_user_id (user_id)`
4. 表之间的关联关系应在实体类或数据访问层代码中定义和维护
5. 在删除"一"方数据前，应先检查是否有关联的"多"方数据，避免出现孤立数据
6. 对于多对多关系的中间表，两个外键字段都应添加索引，并可能需要添加联合唯一索引
   - 例如：`UNIQUE KEY idx_user_role (user_id, role_id)`

## SQL生成规则
1. 表定义语句以`CREATE TABLE `开头
2. 主键定义使用`PRIMARY KEY (`id`) USING BTREE`
3. 表定义最后需要包含引擎和字符集设置：
   - `ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci` 或
   - `ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`
4. 所有字段都应添加`COMMENT`注释，描述字段用途
5. 表定义最后应添加`COMMENT`注释，描述表的用途
6. 对于有默认值的字段，应明确指定默认值
7. 对于非空字段，应添加`NOT NULL`约束
8. 对于有特殊格式要求的字段，可以使用`CHECK`约束

## 字段名称翻译规则
当给定中文字段名时，按照以下方式翻译为英文字段名：

1. 标识/编号类：
   - 编号/编码/代码：`code` 或 `number`
   - ID/标识：`id`
   - 序号：`serial_number`
   - 唯一标识：`uuid`

2. 名称类：
   - 名称/姓名：`name`
   - 昵称：`nickname`
   - 标题：`title`
   - 描述：`description`
   - 备注：`remark`

3. 状态类：
   - 状态：`status`
   - 类型：`type`
   - 是否+动词：`is_` + 英文动词
   - 级别/等级：`level`

4. 时间日期类：
   - 时间/日期：`time`或`date`
   - 开始时间/日期：`start_time`或`start_date`
   - 结束时间/日期：`end_time`或`end_date`
   - 创建时间：`create_time`
   - 更新时间：`update_time`
   - 登录时间：`login_time`或`login_date`
   - 注册时间：`register_time`
   - 过期时间：`expire_time`

5. 数量金额类：
   - 数量：`count`或`quantity`
   - 金额：`amount`
   - 价格：`price`
   - 余额：`balance`
   - 费用：`fee`
   - 利息：`interest`
   - 滞纳金：`late_fee`

6. 联系方式类：
   - 电话/手机：`phone`或`mobile`
   - 座机：`telephone`
   - 地址：`address`
   - 详细地址：`detail_address`
   - 邮箱：`email`
   - IP地址：`ip`

7. 图片附件类：
   - 图片：`image`或`picture`
   - 头像：`avatar`
   - 附件：`attachment`
   - 链接/URL：`url`
   - 文件：`file`

8. 人员机构类：
   - 用户：`user`
   - 客户：`customer`
   - 会员：`member`
   - 部门：`dept`或`department`
   - 公司/机构：`company`或`organization`
   - 角色：`role`
   - 岗位：`post`
   - 法人代表：`legal_representative`或`legal_person`

9. 业务特定类：
   - 债务人：`debtor`
   - 债权人：`creditor`
   - 合同：`contract`
   - 案件：`case`
   - 委托：`entrust`
   - 支付：`pay`或`payment`
   - 退款：`refund`
   - 钱包：`wallet`

## 枚举值和状态码规范
1. 状态类字段应在注释中明确说明各个状态值的含义，例如：
   - `status tinyint NOT NULL DEFAULT '0' COMMENT '状态：0-正常，1-禁用'`
2. 对于使用字符串表示的状态或类型，应在注释中说明对应的业务含义：
   - `type varchar(50) DEFAULT NULL COMMENT '类型：draft-草稿，published-已发布'`
3. 对于来自数据字典的枚举值，应在注释中注明字典名称：
   - `profession varchar(50) DEFAULT NULL COMMENT '职业（来自字典【case_profession】）'`

## 示例SQL生成
```sql
CREATE TABLE `module_entity` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `code` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '编码',
  `type` tinyint DEFAULT '0' COMMENT '类型：0-类型一，1-类型二',
  `status` tinyint DEFAULT '0' COMMENT '状态：0-正常，1-禁用',
  `amount` decimal(18,2) DEFAULT '0.00' COMMENT '金额',
  `start_date` date DEFAULT NULL COMMENT '开始日期',
  `end_date` date DEFAULT NULL COMMENT '结束日期',
  `description` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
  `image_url` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '图片链接',
  `is_valid` tinyint(1) DEFAULT '1' COMMENT '是否有效',
  `remark` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `creator` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建人',
  `updater` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_code` (`code`) USING BTREE,
  KEY `idx_name` (`name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='模块实体表';
```

## 表关联设计规范
1. 一对多关系：在"多"的一方添加外键字段，指向"一"的一方的主键
   - 例如：用户与订单的关系，在订单表中添加`user_id`字段
2. 多对多关系：创建中间表，包含两个外键字段，分别指向两个关联表的主键
   - 例如：用户与角色的关系，创建`system_user_role`表，包含`user_id`和`role_id`字段
3. 外键字段命名应遵循"关联表名_id"的格式，例如：`user_id`、`role_id`
4. 对于关联表，表名应当能反映两个实体之间的关系，例如：`user_role`、`case_entrust_user`

## 软删除与数据审计
1. 所有业务表都应支持软删除机制，通过`deleted`字段标记记录是否被删除
2. 所有业务表都应包含数据审计字段：`creator`、`create_time`、`updater`、`update_time`
3. 对于重要业务操作，应考虑设计专门的日志表记录操作历史
   - 例如：`system_operate_log`、`contract_approval_record`
