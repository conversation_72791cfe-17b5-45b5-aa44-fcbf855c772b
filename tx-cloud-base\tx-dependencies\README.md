# tx-dependencies

## 模块介绍

tx-dependencies 是 tx-cloud-base 项目的基础 BOM (Bill of Materials) 文件，负责管理整个项目的依赖版本。该模块确保所有子模块使用一致的依赖版本，避免版本冲突，简化依赖管理。

## 核心功能

- **统一依赖版本管理**：集中定义所有第三方库和框架的版本号
- **依赖管理（dependencyManagement）**：提供项目所需的所有依赖声明，但不实际引入依赖
- **Maven 插件管理**：统一配置项目使用的 Maven 插件版本和行为
- **版本号管理**：使用 Maven 属性统一管理版本号，便于升级和维护

## 技术栈版本

tx-dependencies 管理了以下主要技术栈的版本：

### 核心框架
- Spring Boot: 3.4.1
- Spring Cloud: 2024.0.0
- Spring Cloud Alibaba: 2023.0.3.2

### Web 相关
- SpringDoc: 2.7.0
- Knife4j: 4.6.0

### 数据库相关
- Druid: 1.2.24
- MyBatis: 3.5.17
- MyBatis Plus: 3.5.9
- Redisson: 3.41.0

### 消息队列
- RocketMQ Spring: 2.3.1

### 定时任务
- XXL-Job: 2.4.0

### 工作流
- Flowable: 7.0.1

### 工具类
- Lombok: 1.18.36
- MapStruct: 1.6.3
- Hutool: 5.8.35/6.0.0-M19
- EasyExcel: 4.0.3
- Guava: 33.4.0-jre

### 三方云服务
- AWS S3: 1.12.777
- JustAuth: 2.0.5
- WxJava: 4.6.0

## 使用指南

### 作为父模块引入

在项目的根 pom.xml 中引入 tx-dependencies 作为父模块：

```xml
<parent>
    <groupId>cn.tianxing.cloud</groupId>
    <artifactId>tx-dependencies</artifactId>
    <version>${revision}</version>
    <relativePath/>
</parent>
```

### 作为依赖管理引入

在项目的 pom.xml 中引入 tx-dependencies 作为依赖管理：

```xml
<dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>cn.tianxing.cloud</groupId>
            <artifactId>tx-dependencies</artifactId>
            <version>${revision}</version>
            <type>pom</type>
            <scope>import</scope>
        </dependency>
    </dependencies>
</dependencyManagement>
```

### 使用统一管理的依赖

引入 tx-dependencies 后，可以直接使用其管理的依赖，无需指定版本号：

```xml
<dependencies>
    <!-- Web 相关 -->
    <dependency>
        <groupId>cn.tianxing.cloud</groupId>
        <artifactId>tx-spring-boot-starter-web</artifactId>
    </dependency>
    
    <!-- DB 相关 -->
    <dependency>
        <groupId>cn.tianxing.cloud</groupId>
        <artifactId>tx-spring-boot-starter-mybatis</artifactId>
    </dependency>
    
    <!-- 工具类 -->
    <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <scope>provided</scope>
    </dependency>
</dependencies>
```

## 版本管理

tx-dependencies 使用 Maven 属性统一管理版本号，便于升级和维护：

```xml
<properties>
    <revision>2.4.1-SNAPSHOT</revision>
    <spring.boot.version>3.4.1</spring.boot.version>
    <spring.cloud.version>2024.0.0</spring.cloud.version>
    <spring.cloud.alibaba.version>2023.0.3.2</spring.cloud.alibaba.version>
    <!-- 其他版本属性 -->
</properties>
```

### 版本号更新流程

1. 修改 `<revision>` 属性更新项目整体版本
2. 修改对应的依赖版本属性更新特定依赖版本
3. 执行 `mvn clean install` 将更新后的 BOM 安装到本地仓库
4. 提交更改并推送到代码仓库

## Maven 插件配置

tx-dependencies 配置了以下 Maven 插件：

1. **maven-deploy-plugin**：用于部署项目构件到远程仓库
2. **flatten-maven-plugin**：用于统一管理 `${revision}` 版本属性

## 分发管理

tx-dependencies 配置了项目的分发管理，指定了快照版本和正式版本的部署仓库：

```xml
<distributionManagement>
    <snapshotRepository>
        <id>maven-snapshots</id>
        <url>http://172.16.116.161:8081/repository/maven-snapshots/</url>
    </snapshotRepository>
    <repository>
        <id>maven-releases</id>
        <url>http://172.16.116.161:8081/repository/maven-releases/</url>
    </repository>
</distributionManagement>
```

## 最佳实践

1. **版本升级**：升级依赖版本时，先在测试环境验证兼容性，再更新到生产环境
2. **添加新依赖**：添加新依赖时，在 tx-dependencies 中统一管理版本号
3. **依赖冲突解决**：使用 `mvn dependency:tree` 命令检查依赖树，解决版本冲突
4. **定期更新**：定期检查并更新依赖版本，修复安全漏洞和 bug

## 常见问题

1. **版本冲突**：如果遇到依赖版本冲突，可以使用 `<exclusions>` 标签排除冲突的传递依赖
2. **找不到依赖**：确保 Maven 仓库配置正确，包含所有必要的仓库地址
3. **构建失败**：检查 Maven 设置和网络连接，确保能够下载所有依赖
