<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="GrepConsole">
    <option name="pinnedGrepConsolesState">
      <PinnedGrepConsolesState>
        <option name="map">
          <map>
            <entry>
              <key>
                <RunConfigurationRef>
                  <option name="icon" value="SpringBoot.svg')" />
                  <option name="name" value="SystemServerApplication" />
                </RunConfigurationRef>
              </key>
              <value>
                <Pins>
                  <option name="pins">
                    <list>
                      <Pin>
                        <option name="consoleUUID" value="2ad2b8e6-cbc9-4158-ba25-f4a12f218d19" />
                        <option name="contentType" value="ConsoleContent" />
                        <option name="grepCompositeModel">
                          <GrepCompositeModel>
                            <option name="beforeAfterModel">
                              <GrepBeforeAfterModel>
                                <option name="after" value="10" />
                                <option name="before" value="10" />
                              </GrepBeforeAfterModel>
                            </option>
                            <option name="models">
                              <list>
                                <GrepModel>
                                  <option name="expression" value="access_token" />
                                </GrepModel>
                              </list>
                            </option>
                          </GrepCompositeModel>
                        </option>
                      </Pin>
                    </list>
                  </option>
                </Pins>
              </value>
            </entry>
            <entry>
              <key>
                <RunConfigurationRef>
                  <option name="icon" value="SpringBoot.svg')" />
                  <option name="name" value="GatewayServerApplication" />
                </RunConfigurationRef>
              </key>
              <value>
                <Pins />
              </value>
            </entry>
          </map>
        </option>
      </PinnedGrepConsolesState>
    </option>
  </component>
</project>