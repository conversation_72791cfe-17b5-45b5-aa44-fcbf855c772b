
CREATE TABLE IF NOT EXISTS "infra_config" (
    "id" bigint(20) NOT NULL GENERATED BY DEFAULT AS IDENTITY COMMENT '编号',
    "category" varchar(50) NOT NULL,
    "type" tinyint NOT NULL,
    "name" varchar(100) NOT NULL DEFAULT '' COMMENT '名字',
    "config_key" varchar(100) NOT NULL DEFAULT '',
    "value" varchar(500) NOT NULL DEFAULT '',
    "visible" bit NOT NULL,
    "remark" varchar(500) DEFAULT NULL,
    "creator" varchar(64) DEFAULT '',
    "create_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updater" varchar(64) DEFAULT '',
    "update_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted" bit NOT NULL DEFAULT FALSE,
    PRIMARY KEY ("id")
) COMMENT '参数配置表';

CREATE TABLE IF NOT EXISTS "infra_file_config" (
    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "name" varchar(63) NOT NULL,
    "storage" tinyint NOT NULL,
    "remark" varchar(255),
    "master" bit(1) NOT NULL,
    "config" varchar(4096) NOT NULL,
    "creator" varchar(64) DEFAULT '',
    "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updater" varchar(64) DEFAULT '',
    "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    "deleted" bit NOT NULL DEFAULT FALSE,
    PRIMARY KEY ("id")
) COMMENT '文件配置表';

CREATE TABLE IF NOT EXISTS "infra_file" (
    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "config_id" bigint NOT NULL,
    "name" varchar(256),
    "path" varchar(512),
    "url" varchar(1024),
    "type" varchar(63) DEFAULT NULL,
    "size" bigint NOT NULL,
    "creator" varchar(64) DEFAULT '',
    "create_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updater" varchar(64) DEFAULT '',
    "update_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted" bit NOT NULL DEFAULT FALSE,
    "tenant_id" bigint not null default  '0',
    PRIMARY KEY ("id")
) COMMENT '文件表';

CREATE TABLE IF NOT EXISTS "infra_job" (
    "id" bigint(20) NOT NULL GENERATED BY DEFAULT AS IDENTITY COMMENT '任务编号',
    "name" varchar(32) NOT NULL COMMENT '任务名称',
    "status" tinyint(4) NOT NULL COMMENT '任务状态',
    "handler_name" varchar(64) NOT NULL COMMENT '处理器的名字',
    "handler_param" varchar(255) DEFAULT NULL COMMENT '处理器的参数',
    "cron_expression" varchar(32) NOT NULL COMMENT 'CRON 表达式',
    "retry_count" int(11) NOT NULL DEFAULT '0' COMMENT '重试次数',
    "retry_interval" int(11) NOT NULL DEFAULT '0' COMMENT '重试间隔',
    "monitor_timeout" int(11) NOT NULL DEFAULT '0' COMMENT '监控超时时间',
    "creator" varchar(64) DEFAULT '' COMMENT '创建者',
    "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    "updater" varchar(64) DEFAULT '' COMMENT '更新者',
    "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    "deleted" bit NOT NULL DEFAULT FALSE COMMENT '是否删除',
    PRIMARY KEY ("id")
) COMMENT='定时任务表';

CREATE TABLE IF NOT EXISTS "infra_job_log" (
    "id" bigint(20) NOT NULL GENERATED BY DEFAULT AS IDENTITY COMMENT '日志编号',
    "job_id" bigint(20) NOT NULL COMMENT '任务编号',
    "handler_name" varchar(64) NOT NULL COMMENT '处理器的名字',
    "handler_param" varchar(255) DEFAULT NULL COMMENT '处理器的参数',
    "execute_index" tinyint(4) NOT NULL DEFAULT '1' COMMENT '第几次执行',
    "begin_time" datetime NOT NULL COMMENT '开始执行时间',
    "end_time" datetime DEFAULT NULL COMMENT '结束执行时间',
    "duration" int(11) DEFAULT NULL COMMENT '执行时长',
    "status" tinyint(4) NOT NULL COMMENT '任务状态',
    "result" varchar(4000) DEFAULT '' COMMENT '结果数据',
    "creator" varchar(64) DEFAULT '' COMMENT '创建者',
    "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    "updater" varchar(64) DEFAULT '' COMMENT '更新者',
    "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    "deleted" bit(1) NOT NULL DEFAULT FALSE COMMENT '是否删除',
    PRIMARY KEY ("id")
)COMMENT='定时任务日志表';

CREATE TABLE IF NOT EXISTS "infra_api_access_log" (
    "id" bigint not null GENERATED BY DEFAULT AS IDENTITY,
    "trace_id" varchar(64) not null default '',
    "user_id" bigint not null default '0',
    "user_type" tinyint not null default '0',
    "application_name" varchar(50) not null,
    "request_method" varchar(16) not null default '',
    "request_url" varchar(255) not null default '',
    "request_params" varchar(8000) not null default '',
    "response_body" varchar(8000) not null default '',
    "user_ip" varchar(50) not null,
    "user_agent" varchar(512) not null,
    `operate_module`           varchar(50)   NOT NULL,
    `operate_name`             varchar(50)   NOT NULL,
    `operate_type`     bigint(4)     NOT NULL DEFAULT '0',
    "begin_time" timestamp not null,
    "end_time" timestamp not null,
    "duration" integer not null,
    "result_code" integer not null default '0',
    "result_msg" varchar(512) default '',
    "creator" varchar(64) default '',
    "create_time" timestamp not null default current_timestamp,
    "updater" varchar(64) default '',
    "update_time" timestamp not null default current_timestamp,
    "deleted" bit not null default false,
    "tenant_id" bigint not null default  '0',
    primary key ("id")
) COMMENT 'API 访问日志表';

CREATE TABLE IF NOT EXISTS "infra_api_error_log" (
    "id" bigint not null GENERATED BY DEFAULT AS IDENTITY,
    "trace_id" varchar(64) not null,
    "user_id" bigint not null default '0',
    "user_type" tinyint not null default '0',
    "application_name" varchar(50) not null,
    "request_method" varchar(16) not null,
    "request_url" varchar(255) not null,
    "request_params" varchar(8000) not null,
    "user_ip" varchar(50) not null,
    "user_agent" varchar(512) not null,
    "exception_time" timestamp not null,
    "exception_name" varchar(128) not null default '',
    "exception_message" clob not null,
    "exception_root_cause_message" clob not null,
    "exception_stack_trace" clob not null,
    "exception_class_name" varchar(512) not null,
    "exception_file_name" varchar(512) not null,
    "exception_method_name" varchar(512) not null,
    "exception_line_number" integer not null,
    "process_status" tinyint not null,
    "process_time" timestamp default null,
    "process_user_id" bigint default '0',
    "creator" varchar(64) default '',
    "create_time" timestamp not null default current_timestamp,
    "updater" varchar(64) default '',
    "update_time" timestamp not null default current_timestamp,
    "deleted" bit not null default false,
    "tenant_id" bigint not null default  '0',
    primary key ("id")
) COMMENT '系统异常日志';

CREATE TABLE IF NOT EXISTS "infra_data_source_config" (
    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "name" varchar(100) NOT NULL,
    "url" varchar(1024) NOT NULL,
    "username" varchar(255) NOT NULL,
    "password" varchar(255) NOT NULL,
    "creator" varchar(64) DEFAULT '',
    "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updater" varchar(64) DEFAULT '',
    "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    "deleted" bit NOT NULL DEFAULT FALSE,
    PRIMARY KEY ("id")
) COMMENT '数据源配置表';

CREATE TABLE IF NOT EXISTS "infra_codegen_table" (
    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "data_source_config_id" bigint not null,
    "scene" tinyint not null DEFAULT 1,
    "table_name" varchar(200) NOT NULL,
    "table_comment" varchar(500) NOT NULL,
    "remark" varchar(500) NOT NULL,
    "module_name" varchar(30) NOT NULL,
    "business_name" varchar(30) NOT NULL,
    "class_name" varchar(100) NOT NULL,
    "class_comment" varchar(50) NOT NULL,
    "author" varchar(50) NOT NULL,
    "template_type" tinyint not null DEFAULT 1,
    "front_type" tinyint not null,
    "parent_menu_id" bigint not null,
    "master_table_id" bigint not null,
    "sub_join_column_id" bigint not null,
    "sub_join_many" bit not null,
    "tree_parent_column_id" bigint not null,
    "tree_name_column_id" bigint not null,
    "creator" varchar(64) DEFAULT '',
    "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updater" varchar(64) DEFAULT '',
    "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    "deleted" bit NOT NULL DEFAULT FALSE,
    PRIMARY KEY ("id")
) COMMENT '代码生成表定义表';

CREATE TABLE IF NOT EXISTS "infra_codegen_column" (
    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "table_id" bigint not null,
    "column_name" varchar(200) NOT NULL,
    "data_type" varchar(100) NOT NULL,
    "column_comment" varchar(500) NOT NULL,
    "nullable" tinyint not null,
    "primary_key" tinyint not null,
    "ordinal_position" int not null,
    "java_type" varchar(32) NOT NULL,
    "java_field" varchar(64) NOT NULL,
    "dict_type" varchar(200) NOT NULL,
    "example" varchar(64) NOT NULL,
    "create_operation" bit not null,
    "update_operation" bit not null,
    "list_operation" bit not null,
    "list_operation_condition" varchar(32) not null,
    "list_operation_result" bit not null,
    "html_type" varchar(32) NOT NULL,
    "creator" varchar(64) DEFAULT '',
    "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updater" varchar(64) DEFAULT '',
    "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    "deleted" bit NOT NULL DEFAULT FALSE,
    PRIMARY KEY ("id")
) COMMENT '代码生成表字段定义表';