{"version": "0.2.0", "configurations": [{"type": "java", "name": "CRM服务", "request": "launch", "mainClass": "cn.tianxing.cloud.module.crm.CrmServerApplication", "projectName": "tx-module-crm-biz", "vmArgs": "-Dspring.profiles.active=dev -Dspring.cloud.nacos.discovery.namespace=tangpanhui -Xms512m -Xmx1024m -Dfile.encoding=UTF-8"}, {"type": "java", "name": "系统服务", "request": "launch", "mainClass": "cn.tianxing.cloud.module.system.SystemServerApplication", "projectName": "tx-module-system-biz", "vmArgs": "-Dspring.profiles.active=dev -Dspring.cloud.nacos.discovery.namespace=tangpanhui -Xms512m -Xmx1024m -Dfile.encoding=UTF-8"}, {"type": "java", "name": "基础设施服务", "request": "launch", "mainClass": "cn.tianxing.cloud.module.infra.InfraServerApplication", "projectName": "tx-module-infra-biz", "vmArgs": "-Dspring.profiles.active=dev -Dspring.cloud.nacos.discovery.namespace=tangpanhui -Xms512m -Xmx1024m -Dfile.encoding=UTF-8"}, {"type": "java", "name": "会员服务", "request": "launch", "mainClass": "cn.tianxing.cloud.module.member.MemberServerApplication", "projectName": "tx-module-member-biz", "vmArgs": "-Dspring.profiles.active=dev -Dspring.cloud.nacos.discovery.namespace=tangpanhui -Xms512m -Xmx1024m -Dfile.encoding=UTF-8"}, {"type": "java", "name": "报表服务", "request": "launch", "mainClass": "cn.tianxing.cloud.module.report.ReportServerApplication", "projectName": "tx-module-report-biz", "vmArgs": "-Dspring.profiles.active=dev -Dspring.cloud.nacos.discovery.namespace=tangpanhui -Xms512m -Xmx1024m -Dfile.encoding=UTF-8"}, {"type": "java", "name": "网关服务", "request": "launch", "mainClass": "cn.tianxing.cloud.gateway.GatewayServerApplication", "projectName": "tx-gateway", "vmArgs": "-Dspring.profiles.active=dev -Dspring.cloud.nacos.discovery.namespace=tangpanhui -Xms512m -Xmx1024m -Dfile.encoding=UTF-8"}, {"type": "java", "name": "Debug Java Application", "request": "launch", "mainClass": "${file}", "env": {"JAVA_TOOL_OPTIONS": "-Dfile.encoding=UTF-8"}}]}