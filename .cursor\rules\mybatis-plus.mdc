---
description: 根据表名创建一套MyBatis-Plus相关文件 (do, service, serviceImpl, mapper, mapper.xml)。
globs: 
alwaysApply: false
---
# mybatis-plus

根据表名，在指定的模块下创建一套完整的 MyBatis-Plus 相关文件。

**使用方法:**
当您说："帮我根据 `<TABLE_NAME>` 表，在 `<MODULE_NAME>` 模块中创建一套相关文件"，我将执行以下操作。

**占位符说明:**
-   `<MODULE_NAME>`: 您指定的模块名称 (例如: `crm`)。
-   `<TABLE_NAME>`: 数据库表的名称 (例如: `student`)，将作为实体的小写名称 (`<LOWER_CASE_NAME>`)。
-   `<PASCAL_CASE_NAME>`: 由表名转换来的 PascalCase 名称 (例如: `Student`)。

---

## 文件结构和规范

### 1. DO (Data Object)

**文件名:** `<PASCAL_CASE_NAME>DO.java`
**存放路径:** `tx-module-<MODULE_NAME>/tx-module-<MODULE_NAME>-biz/src/main/java/cn/tianxing/cloud/module/<MODULE_NAME>/dal/dataobject/<LOWER_CASE_NAME>/`
**包名:** `cn.tianxing.cloud.module.<MODULE_NAME>.dal.dataobject.<LOWER_CASE_NAME>`
**代码要求:**
-   必须继承 `BaseDO`。
-   使用 `@TableName("<TABLE_NAME>")` 注解。
-   主键字段必须使用 `@TableId(type = IdType.ASSIGN_ID)`。
-   所有字段都必须添加 `@Schema` 注解进行说明。
-   必须包含适当的JavaDoc注释。

### 2. Mapper 接口

**文件名:** `<PASCAL_CASE_NAME>Mapper.java`
**存放路径:** `tx-module-<MODULE_NAME>/tx-module-<MODULE_NAME>-biz/src/main/java/cn/tianxing/cloud/module/<MODULE_NAME>/dal/mysql/<LOWER_CASE_NAME>/`
**包名:** `cn.tianxing.cloud.module.<MODULE_NAME>.dal.mysql.<LOWER_CASE_NAME>`
**代码要求:**
-   必须继承 `BaseMapperX<<PASCAL_CASE_NAME>DO>`。
-   必须添加 `@Mapper` 注解。
-   必须包含适当的JavaDoc注释。

### 3. Service 接口

**文件名:** `I<PASCAL_CASE_NAME>Service.java`
**存放路径:** `tx-module-<MODULE_NAME>/tx-module-<MODULE_NAME>-biz/src/main/java/cn/tianxing/cloud/module/<MODULE_NAME>/service/<LOWER_CASE_NAME>/`
**包名:** `cn.tianxing.cloud.module.<MODULE_NAME>.service.<LOWER_CASE_NAME>`
**代码要求:**
-   必须继承 `IService<<PASCAL_CASE_NAME>DO>`。
-   必须包含基本的CRUD方法声明。
-   所有方法必须有详细的JavaDoc注释。

### 4. Service 实现类

**文件名:** `<PASCAL_CASE_NAME>ServiceImpl.java`
**存放路径:** `tx-module-<MODULE_NAME>/tx-module-<MODULE_NAME>-biz/src/main/java/cn/tianxing/cloud/module/<MODULE_NAME>/service/<LOWER_CASE_NAME>/impl/`
**包名:** `cn.tianxing.cloud.module.<MODULE_NAME>.service.<LOWER_CASE_NAME>.impl`
**代码要求:**
-   必须继承 `ServiceImpl<<PASCAL_CASE_NAME>Mapper, <PASCAL_CASE_NAME>DO>`。
-   必须实现 `I<PASCAL_CASE_NAME>Service` 接口。
-   必须添加 `@Service` 注解。
-   必须添加 `@Slf4j` 注解进行日志记录。
-   关键业务方法必须添加适当的日志记录。

### 5. Mapper XML

**文件名:** `<PASCAL_CASE_NAME>Mapper.xml`
**存放路径:** `tx-module-<MODULE_NAME>/tx-module-<MODULE_NAME>-biz/src/main/resources/mapper/<LOWER_CASE_NAME>/`
**代码要求:**
-   `namespace` 必须指向对应的 Mapper 接口 (`cn.tianxing.cloud.module.<MODULE_NAME>.dal.mysql.<LOWER_CASE_NAME>.<PASCAL_CASE_NAME>Mapper`)。
-   必须包含 `resultMap` 定义。
-   建议包含基本的SQL片段。

### 6. Controller 类 (可选)

**文件名:** `<PASCAL_CASE_NAME>Controller.java`
**存放路径:** `tx-module-<MODULE_NAME>/tx-module-<MODULE_NAME>-biz/src/main/java/cn/tianxing/cloud/module/<MODULE_NAME>/controller/<LOWER_CASE_NAME>/`
**包名:** `cn.tianxing.cloud.module.<MODULE_NAME>.controller.<LOWER_CASE_NAME>`
**代码要求:**
-   必须添加 `@RestController` 和 `@RequestMapping` 注解。
-   必须添加 `@Tag` 注解用于Swagger文档。
-   必须注入对应的Service。
-   方法体不超过5行，业务逻辑应在Service实现。
-   所有接口方法必须有 `@Operation` 注解。
-   接口参数必须有 `@Parameter` 或 `@RequestBody` 注解。

### 7. DTO 类 (可选)

**文件名:** `<PASCAL_CASE_NAME>DTO.java`、`<PASCAL_CASE_NAME>CreateDTO.java`、`<PASCAL_CASE_NAME>UpdateDTO.java`等
**存放路径:** `tx-module-<MODULE_NAME>/tx-module-<MODULE_NAME>-biz/src/main/java/cn/tianxing/cloud/module/<MODULE_NAME>/controller/<LOWER_CASE_NAME>/dto/`
**包名:** `cn.tianxing.cloud.module.<MODULE_NAME>.controller.<LOWER_CASE_NAME>.dto`
**代码要求:**
-   所有字段必须添加 `@Schema` 注解。
-   必须添加数据校验注解，如 `@NotNull`、`@Size` 等。
-   必须包含无参构造函数。

### 8. VO 类 (可选)

**文件名:** `<PASCAL_CASE_NAME>VO.java`
**存放路径:** `tx-module-<MODULE_NAME>/tx-module-<MODULE_NAME>-biz/src/main/java/cn/tianxing/cloud/module/<MODULE_NAME>/controller/<LOWER_CASE_NAME>/vo/`
**包名:** `cn.tianxing.cloud.module.<MODULE_NAME>.controller.<LOWER_CASE_NAME>.vo`
**代码要求:**
-   所有字段必须添加 `@Schema` 注解。
-   必须包含无参构造函数。

## 实现流程

1. 首先创建 DO 类，定义实体字段和表映射
2. 创建 Mapper 接口，继承基础接口
3. 创建 Mapper XML 文件，配置基本SQL
4. 创建 Service 接口，定义业务方法
5. 创建 Service 实现类，实现业务逻辑
6. 创建 DTO/VO 类，用于数据传输
7. 创建 Controller 类，提供 API 接口

## 注意事项

1. 创建的类必须遵循项目中已有的命名和代码风格
2. 包结构应该与项目现有结构保持一致
3. 生成的文件应该包含必要的引用和注释
4. 确保生成的代码符合项目的编码规范
5. 复杂的业务逻辑应该在 Service 层实现，而不是在 Controller 层