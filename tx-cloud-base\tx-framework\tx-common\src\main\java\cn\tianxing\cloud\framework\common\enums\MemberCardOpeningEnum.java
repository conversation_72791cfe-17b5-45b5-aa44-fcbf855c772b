package cn.tianxing.cloud.framework.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 会员卡开通方式枚举
 */
@AllArgsConstructor
@Getter
public enum MemberCardOpeningEnum {
    // 基础条件（1-4）
    USER_REGISTERED("1", "用户注册"),
    APPLICATION_SUBMITTED("2", "申请开通"),
    CREDITOR_ROLE("3", "债权人"),
    DEBTOR_ROLE("4", "债务人"),

    // 白银级条件（5-9）
    SILVER_CREDIT_OVER_30K("5", "债权额在3万以上"),
    SILVER_REFERRED_PAID_MEMBERS("6", "推荐2位付费会员"),  // 修正编号为6
    SILVER_MIN_5_MEMBERS("7", "成功引荐至少5位会员加入平台"),
    SILVER_MANUFACTURER_PRODUCT("8", "成功上架1款厂家直供商品"),
    SILVER_TOP3_SERVICE_PROVIDER("9", "促成1家当地top3线下服务商入驻"),

    // 黄金级条件（10-14）
    GOLD_MIN_10_MEMBERS("10", "引荐至少10位平台会员"),
    GOLD_SALES_5M("11", "累计500万+销售额"),
    GOLD_MANUFACTURER_HIT_PRODUCTS("12", "5款销售额20万+厂家直供商品"),
    GOLD_SERVICE_PROVIDERS("13", "累计签约20家以上当地线下服务商"),
    GOLD_MIN_100_MEMBERS("14", "成功引荐100+平台会员");;

    /**
     * 类型
     */
    private final String type;

    /**
     * 名称
     */
    private final String name;
}
