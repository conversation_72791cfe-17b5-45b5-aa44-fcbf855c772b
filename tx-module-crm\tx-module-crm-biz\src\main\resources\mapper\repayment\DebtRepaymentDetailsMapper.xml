<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.tianxing.cloud.module.crm.dal.mysql.repayment.DebtRepaymentDetailsMapper">


    <select id="getMonthDebtDetail"
            resultType="cn.tianxing.cloud.module.crm.controller.admin.repayment.vo.DebtRepaymentDetailsRespVO">
        SELECT d.description,d.id,d.assist_id,d.type,d.debt_type,d.create_time,d.mount,d.order_number,d.create_time,
               mu.username as assistUsername
        FROM debt_repayment_details d left join member_user mu on d.assist_id = mu.id and mu.deleted = 0
        WHERE d.deleted = 0
        <if test="month != null and month != ''">
            AND DATE_FORMAT(d.create_time, '%Y%m') = #{month}
        </if>
        <if test="memberId != null">
            AND d.member_user_id = #{memberId}
        </if>
    </select>

    <!-- 查询-总金额 -->
    <select id="selectSumMarningsMoney" resultType="java.math.BigDecimal">
        SELECT
            IFNULL(SUM(mount), 0)
        FROM
            debt_repayment_details
        WHERE
            deleted = 0
          AND member_user_id = #{memberUserId}
          AND type = #{type}
    </select>

    <!-- 查询-金额收入明细 -->
    <select id="getMonthEarningDetail" resultType="cn.tianxing.cloud.module.crm.controller.app.earnings.vo.AppEarningsMoneyDetailVo">
        SELECT
            drd.id,
            drd.debt_type,
            drd.type,
            drd.mount,
            drd.create_time,
            "" AS `name`,
            drd.order_number,
            "" AS cheatingAccount,
            "" AS inviteAccount,
            NULL AS auditStatus,
            0 AS type
        FROM
            debt_repayment_details drd
        WHERE
            drd.deleted = 0
          AND drd.type = 0
          AND drd.debt_type = '0'
          AND drd.member_user_id = #{memberUserId}

        UNION

        SELECT
            drd.id,
            drd.debt_type,
            drd.type,
            drd.mount,
            drd.create_time,
            mu.nickname AS `name`,
            "" AS order_number,
            mu.username AS cheatingAccount,
            "" AS inviteAccount,
            NULL AS auditStatus,
            0 AS type
        FROM
            debt_repayment_details drd
                LEFT JOIN ( SELECT id, username, nickname FROM member_user WHERE deleted = 0 ) mu ON drd.member_user_id = mu.id
        WHERE
            drd.deleted = 0
          AND drd.type = 0
          AND drd.debt_type = '1'
          AND drd.member_user_id = #{memberUserId}

        UNION

        SELECT
            drd.id,
            drd.debt_type,
            drd.type,
            drd.mount,
            drd.create_time,
            "" AS `name`,
            "" AS order_number,
            "" AS cheatingAccount,
            "" AS inviteAccount,
            NULL AS auditStatus,
            0 AS type
        FROM
            debt_repayment_details drd
                LEFT JOIN ( SELECT id, username, nickname FROM member_user WHERE deleted = 0 ) mu ON drd.member_user_id = mu.id
        WHERE
            drd.deleted = 0
          AND drd.type = 0
          AND drd.debt_type = '2'
          AND drd.member_user_id = #{memberUserId}
    </select>

    <select id="selectByOrderNumberAndRefundStatus" resultType="cn.tianxing.cloud.module.crm.dal.dataobject.repayment.DebtRepaymentDetailsDO">
        SELECT *
        FROM debt_repayment_details
        WHERE deleted = 0
          AND order_number = #{orderNumber}
          AND refund_status = #{refundStatus}
        LIMIT 1
    </select>

</mapper>