<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="Encoding">
    <file url="file://$APPLICATION_HOME_DIR$/bin/src/main/java" charset="UTF-8" />
    <file url="file://$APPLICATION_HOME_DIR$/bin/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-framework/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-framework/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-framework/tx-common/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-framework/tx-common/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-framework/tx-spring-boot-starter-biz-data-permission/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-framework/tx-spring-boot-starter-biz-data-permission/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-framework/tx-spring-boot-starter-biz-ip/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-framework/tx-spring-boot-starter-biz-ip/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-framework/tx-spring-boot-starter-biz-tenant/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-framework/tx-spring-boot-starter-biz-tenant/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-framework/tx-spring-boot-starter-env/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-framework/tx-spring-boot-starter-env/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-framework/tx-spring-boot-starter-excel/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-framework/tx-spring-boot-starter-excel/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-framework/tx-spring-boot-starter-job/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-framework/tx-spring-boot-starter-job/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-framework/tx-spring-boot-starter-minio/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-framework/tx-spring-boot-starter-minio/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-framework/tx-spring-boot-starter-monitor/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-framework/tx-spring-boot-starter-monitor/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-framework/tx-spring-boot-starter-mq/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-framework/tx-spring-boot-starter-mq/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-framework/tx-spring-boot-starter-mybatis/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-framework/tx-spring-boot-starter-mybatis/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-framework/tx-spring-boot-starter-protection/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-framework/tx-spring-boot-starter-protection/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-framework/tx-spring-boot-starter-redis/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-framework/tx-spring-boot-starter-redis/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-framework/tx-spring-boot-starter-rpc/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-framework/tx-spring-boot-starter-rpc/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-framework/tx-spring-boot-starter-security/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-framework/tx-spring-boot-starter-security/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-framework/tx-spring-boot-starter-test/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-framework/tx-spring-boot-starter-test/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-framework/tx-spring-boot-starter-web/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-framework/tx-spring-boot-starter-web/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-framework/tx-spring-boot-starter-websocket/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-framework/tx-spring-boot-starter-websocket/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-gateway/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-gateway/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-module-infra/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-module-infra/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-module-infra/tx-module-infra-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-module-infra/tx-module-infra-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-module-infra/tx-module-infra-biz/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-module-infra/tx-module-infra-biz/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-module-system/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-module-system/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-module-system/tx-module-system-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-module-system/tx-module-system-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-module-system/tx-module-system-biz/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-cloud-base/tx-module-system/tx-module-system-biz/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-module-crm/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-module-crm/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-module-crm/tx-module-crm-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-module-crm/tx-module-crm-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-module-crm/tx-module-crm-biz/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-module-crm/tx-module-crm-biz/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-module-member/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-module-member/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-module-member/tx-module-member-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-module-member/tx-module-member-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-module-member/tx-module-member-biz/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-module-member/tx-module-member-biz/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-module-report/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-module-report/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-module-report/tx-module-report-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-module-report/tx-module-report-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-module-report/tx-module-report-biz/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/tx-module-report/tx-module-report-biz/src/main/resources" charset="UTF-8" />
  </component>
</project>