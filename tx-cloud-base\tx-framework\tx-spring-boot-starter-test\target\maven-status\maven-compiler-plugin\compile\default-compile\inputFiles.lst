E:\tph\xm\back-end2\tx-cloud-base\tx-framework\tx-spring-boot-starter-test\src\main\java\cn\tianxing\cloud\framework\test\config\RedisTestConfiguration.java
E:\tph\xm\back-end2\tx-cloud-base\tx-framework\tx-spring-boot-starter-test\src\main\java\cn\tianxing\cloud\framework\test\config\SqlInitializationTestConfiguration.java
E:\tph\xm\back-end2\tx-cloud-base\tx-framework\tx-spring-boot-starter-test\src\main\java\cn\tianxing\cloud\framework\test\core\ut\BaseDbAndRedisUnitTest.java
E:\tph\xm\back-end2\tx-cloud-base\tx-framework\tx-spring-boot-starter-test\src\main\java\cn\tianxing\cloud\framework\test\core\ut\BaseDbUnitTest.java
E:\tph\xm\back-end2\tx-cloud-base\tx-framework\tx-spring-boot-starter-test\src\main\java\cn\tianxing\cloud\framework\test\core\ut\BaseMockitoUnitTest.java
E:\tph\xm\back-end2\tx-cloud-base\tx-framework\tx-spring-boot-starter-test\src\main\java\cn\tianxing\cloud\framework\test\core\ut\BaseRedisUnitTest.java
E:\tph\xm\back-end2\tx-cloud-base\tx-framework\tx-spring-boot-starter-test\src\main\java\cn\tianxing\cloud\framework\test\core\ut\package-info.java
E:\tph\xm\back-end2\tx-cloud-base\tx-framework\tx-spring-boot-starter-test\src\main\java\cn\tianxing\cloud\framework\test\core\util\AssertUtils.java
E:\tph\xm\back-end2\tx-cloud-base\tx-framework\tx-spring-boot-starter-test\src\main\java\cn\tianxing\cloud\framework\test\core\util\RandomUtils.java
E:\tph\xm\back-end2\tx-cloud-base\tx-framework\tx-spring-boot-starter-test\src\main\java\cn\tianxing\cloud\framework\test\package-info.java
