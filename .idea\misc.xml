<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/tx-cloud-base/pom.xml" />
        <option value="$PROJECT_DIR$/tx-module-crm/pom.xml" />
        <option value="$PROJECT_DIR$/tx-module-member/pom.xml" />
        <option value="$PROJECT_DIR$/tx-module-report/pom.xml" />
      </list>
    </option>
    <option name="ignoredFiles">
      <set>
        <option value="$PROJECT_DIR$/tx-cloud-base/tx-dependencies/pom.xml" />
        <option value="$PROJECT_DIR$/tx-cloud-base/tx-framework/pom.xml" />
        <option value="$PROJECT_DIR$/tx-cloud-base/tx-framework/tx-spring-boot-starter-env/pom.xml" />
        <option value="$PROJECT_DIR$/tx-cloud-base/tx-module-infra/pom.xml" />
        <option value="$PROJECT_DIR$/tx-cloud-base/tx-module-system/pom.xml" />
      </set>
    </option>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_17" default="true" project-jdk-name="17" project-jdk-type="JavaSDK" />
</project>