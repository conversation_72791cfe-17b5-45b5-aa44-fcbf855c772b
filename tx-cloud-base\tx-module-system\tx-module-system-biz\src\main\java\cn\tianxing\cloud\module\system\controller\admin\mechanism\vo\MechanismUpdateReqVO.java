package cn.tianxing.cloud.module.system.controller.admin.mechanism.vo;

import cn.tianxing.cloud.module.system.dal.dataobject.reduction.ReductionAmountConfigDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@Schema(description = "管理后台 - 机构更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MechanismUpdateReqVO extends MechanismBaseVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;


    @Schema(description = "减免金额配置列表")
    private List<ReductionAmountConfigDO> reductionAmountConfigList;
} 