# 服务启动规则

## 启动单个服务

### 启动网关服务
```bash
cd tx-cloud-base/tx-gateway && mvn spring-boot:run -Dspring.profiles.active=dev -Dspring.cloud.nacos.discovery.namespace=tangpanhui
```

### 启动报表服务
```bash
cd tx-module-report/tx-module-report-api && mvn clean install -DskipTests
cd tx-module-report/tx-module-report-biz && mvn spring-boot:run -Dspring.profiles.active=dev -Dspring.cloud.nacos.discovery.namespace=tangpanhui
```

### 启动CRM服务
```bash
cd tx-module-crm/tx-module-crm-api && mvn clean install -DskipTests
cd tx-module-crm/tx-module-crm-biz && mvn spring-boot:run -Dspring.profiles.active=dev -Dspring.cloud.nacos.discovery.namespace=tangpanhui
```

### 启动系统服务
```bash
cd tx-cloud-base/tx-module-system/tx-module-system-api && mvn clean install -DskipTests
cd tx-cloud-base/tx-module-system/tx-module-system-biz && mvn spring-boot:run -Dspring.profiles.active=dev -Dspring.cloud.nacos.discovery.namespace=tangpanhui
```

### 启动基础设施服务
```bash
cd tx-cloud-base/tx-module-infra/tx-module-infra-api && mvn clean install -DskipTests
cd tx-cloud-base/tx-module-infra/tx-module-infra-biz && mvn spring-boot:run -Dspring.profiles.active=dev -Dspring.cloud.nacos.discovery.namespace=tangpanhui
```

### 启动会员服务
```bash
cd tx-module-member/tx-module-member-api && mvn clean install -DskipTests
cd tx-module-member/tx-module-member-biz && mvn spring-boot:run -Dspring.profiles.active=dev -Dspring.cloud.nacos.discovery.namespace=tangpanhui
```

## 启动全部服务

要启动全部服务，请按照以下顺序依次启动：

1. 先启动基础服务：
   - 系统服务
   - 基础设施服务
   - 网关服务

2. 再启动业务服务：
   - 会员服务
   - CRM服务
   - 报表服务

## 关闭服务

### 关闭单个服务
在运行服务的终端窗口中按 `Ctrl + C`，然后根据提示输入 `Y` 确认关闭。

### 关闭全部服务
依次在每个服务的终端窗口中按 `Ctrl + C`，然后根据提示输入 `Y` 确认关闭。

或者使用以下命令一次性关闭所有Java进程：
```bash
taskkill /F /IM java.exe
```

## 服务端口

- 网关服务: 48080
- 系统服务: 48081
- 基础设施服务: 48082
- 会员服务: 48083
- 报表服务: 48084
- CRM服务: 48085

## 注意事项

1. 确保启动服务前，相关的API模块已经构建成功
2. 如果遇到依赖问题，可以尝试清理并重新构建整个项目：`mvn clean install -DskipTests`
3. 启动服务时使用dev环境配置和tangpanhui命名空间
4. 如果端口被占用，可以使用`netstat -ano | findstr 端口号`查找占用端口的进程，然后使用`taskkill /F /PID 进程号`关闭 