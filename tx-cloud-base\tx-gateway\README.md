# tx-gateway

## 模块介绍

tx-gateway 是 tx-cloud-base 项目的 API 服务网关模块，基于 Spring Cloud Gateway 实现。作为整个微服务架构的入口，它提供了路由转发、负载均衡、API 聚合、安全控制等核心功能。

## 主要功能

- **路由管理**：动态配置服务路由，支持路径重写、前缀过滤等
- **负载均衡**：基于 Spring Cloud LoadBalancer 实现服务负载均衡
- **API 文档聚合**：集成 Knife4j，聚合展示各个微服务的 API 文档
- **服务发现**：基于 Nacos 的服务注册与发现
- **动态配置**：基于 Nacos 的配置中心，支持配置动态更新
- **安全控制**：提供统一的认证授权入口
- **流量控制**：支持限流、熔断等流量控制策略
- **监控告警**：集成监控功能，实时监控网关状态

## 技术栈

- **核心框架**：Spring Cloud Gateway
- **服务发现**：Nacos Discovery
- **配置中心**：Nacos Config
- **负载均衡**：Spring Cloud LoadBalancer
- **API 文档**：Knife4j Gateway
- **监控**：Spring Boot Actuator + Prometheus + Grafana

## 配置说明

### 路由配置

在 `application.yml` 或 Nacos 配置中心中配置路由规则：

```yaml
spring:
  cloud:
    gateway:
      routes:
        - id: system-service
          uri: lb://tx-module-system
          predicates:
            - Path=/system/**
          filters:
            - StripPrefix=1
```

### 跨域配置

```yaml
spring:
  cloud:
    gateway:
      globalcors:
        cors-configurations:
          '[/**]':
            allowedOrigins: "*"
            allowedMethods: "*"
            allowedHeaders: "*"
```

### 限流配置

```yaml
spring:
  cloud:
    gateway:
      routes:
        - id: system-service
          uri: lb://tx-module-system
          predicates:
            - Path=/system/**
          filters:
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 10
                redis-rate-limiter.burstCapacity: 20
```

## 部署说明

### 环境要求

- JDK 17+
- Nacos 服务端

### 构建与运行

```bash
# 构建
mvn clean package -DskipTests

# 运行
java -jar tx-gateway.jar
```

### Docker 部署

项目提供了 Dockerfile，可以构建 Docker 镜像进行部署：

```bash
# 构建镜像
docker build -t tx-gateway .

# 运行容器
docker run -p 8080:8080 -e NACOS_SERVER=nacos-server:8848 tx-gateway
```

## 扩展开发

### 自定义过滤器

创建自定义 GatewayFilter：

```java
@Component
public class CustomGatewayFilterFactory extends AbstractGatewayFilterFactory<CustomGatewayFilterFactory.Config> {
    
    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {
            // 自定义逻辑
            return chain.filter(exchange);
        };
    }
    
    public static class Config {
        // 配置属性
    }
}
```

### 自定义谓词

创建自定义 RoutePredicateFactory：

```java
@Component
public class CustomRoutePredicateFactory extends AbstractRoutePredicateFactory<CustomRoutePredicateFactory.Config> {
    
    @Override
    public Predicate<ServerWebExchange> apply(Config config) {
        return exchange -> {
            // 自定义逻辑
            return true;
        };
    }
    
    public static class Config {
        // 配置属性
    }
}
```

## 常见问题

1. **路由不生效**：检查路由配置是否正确，服务名是否与注册中心一致
2. **服务调用失败**：检查服务是否正常注册到 Nacos，网络是否通畅
3. **跨域问题**：检查跨域配置是否正确
4. **内存溢出**：调整 JVM 参数，增加内存配置

## 监控指标

网关模块暴露了以下监控指标：

- 请求总数
- 请求延迟
- 错误率
- 路由使用情况
- JVM 内存使用情况

可通过 Actuator 端点 `/actuator/metrics` 查看详细指标。
