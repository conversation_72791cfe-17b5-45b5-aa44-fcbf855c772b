---
description: 所有操作应该遵循的规则!
globs: 
alwaysApply: false
---
# 所有操作应该遵循的规则!

## 交互式反馈机制
1. 在任何流程、任务、对话进行时，无论是询问、回复、或完成阶段性任务，皆必须调用 MCP mcp-feedback-enhanced。
2. 每当收到用户反馈，若反馈内容非空，必须再次调用 MCP mcp-feedback-enhanced，并根据反馈内容调整行为。
3. 仅当用户明确表示「结束」或「不再需要交互」时，才可停止调用 MCP mcp-feedback-enhanced，流程才算结束。
4. 除非收到结束指令，否则所有步骤都必须重复调用 MCP mcp-feedback-enhanced。

## 任务执行规范
1. 完成任务前，必须使用 MCP mcp-feedback-enhanced 工具向用户询问反馈。
2. 当做出重大改变或设计决策时，需要提供设计方案并询问用户意见。
3. 在执行复杂任务时，应分步骤执行并在每个关键步骤后获取用户确认。
4. 当面临多种实现方案时，应提供方案对比并请求用户选择。

## 响应式行为
1. 当用户提出新需求时，应立即响应并确认理解是否正确。
2. 当遇到技术障碍时，应明确说明问题并提供可能的解决方案。
3. 当需要更多信息时，应明确列出所需的具体信息。
4. 在每个重要决策点，应提供足够上下文让用户做出明智决定。

## 沟通风格
1. 使用专业且简洁的语言，避免过于冗长的解释。
2. 在提供代码实现时，应同时解释关键设计决策和技术选择。
3. 保持一致的交互模式，让用户习惯于特定的反馈-响应循环。
4. 在每个交互环节结束时，明确指出下一步可能的行动方向。