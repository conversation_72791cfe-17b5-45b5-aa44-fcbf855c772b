# tx-framework

## 模块介绍

tx-framework 是 tx-cloud-base 项目的技术组件集合，提供了一系列可复用的技术组件和业务组件，为整个项目提供基础支持。每个子模块代表一个独立的组件，可以根据需要单独引入使用。

## 组件分类

tx-framework 中的组件分为两大类：

1. **框架组件**：对常用框架（如 MyBatis、Redis 等）的扩展和增强
2. **业务组件**：与业务相关的通用功能封装（如多租户、数据权限等），这类组件的 Maven 名称中包含 `biz`

## 组件结构

每个组件通常包含两部分：
- **core 包**：组件的核心封装，包含核心功能实现
- **config 包**：组件基于 Spring 的配置，提供自动配置能力

## 核心组件

### 基础组件

- **tx-common**：通用工具类和基础功能
- **tx-spring-boot-starter-env**：环境配置组件
- **tx-spring-boot-starter-web**：Web 开发组件
- **tx-spring-boot-starter-test**：测试工具组件

### 数据访问组件

- **tx-spring-boot-starter-mybatis**：MyBatis 增强组件
- **tx-spring-boot-starter-redis**：Redis 缓存组件
- **tx-spring-boot-starter-minio**：MinIO 对象存储组件

### 安全组件

- **tx-spring-boot-starter-security**：安全认证组件
- **tx-spring-boot-starter-protection**：系统保护组件

### 通信组件

- **tx-spring-boot-starter-websocket**：WebSocket 支持组件
- **tx-spring-boot-starter-rpc**：远程调用组件

### 任务与消息组件

- **tx-spring-boot-starter-job**：定时任务和异步任务组件
- **tx-spring-boot-starter-mq**：消息队列组件

### 监控组件

- **tx-spring-boot-starter-monitor**：系统监控组件

### 业务组件

- **tx-spring-boot-starter-biz-tenant**：多租户支持组件
- **tx-spring-boot-starter-biz-data-permission**：数据权限组件
- **tx-spring-boot-starter-biz-ip**：IP 地址处理组件

### 工具组件

- **tx-spring-boot-starter-excel**：Excel 导入导出组件

## 使用方式

在项目中引入需要的组件依赖：

```xml
<!-- 示例：引入 Web 组件 -->
<dependency>
    <groupId>cn.tianxing.cloud</groupId>
    <artifactId>tx-spring-boot-starter-web</artifactId>
    <version>${revision}</version>
</dependency>

<!-- 示例：引入 MyBatis 组件 -->
<dependency>
    <groupId>cn.tianxing.cloud</groupId>
    <artifactId>tx-spring-boot-starter-mybatis</artifactId>
    <version>${revision}</version>
</dependency>
```

## 扩展开发

如需开发新的组件，建议遵循以下规范：

1. 在 tx-framework 下创建新的模块，命名为 `tx-spring-boot-starter-xxx`
2. 如果是业务相关组件，命名为 `tx-spring-boot-starter-biz-xxx`
3. 组件内部包结构建议分为 `core` 和 `config` 两部分
4. 提供自动配置类，命名为 `XxxAutoConfiguration`
5. 在 `META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports` 文件中注册自动配置类

## 注意事项

- 组件之间应尽量减少耦合，保持独立性
- 公共依赖应在 tx-dependencies 中统一管理版本
- 组件应提供完善的文档和使用示例
- 业务组件应避免过度依赖具体业务逻辑，保持通用性
