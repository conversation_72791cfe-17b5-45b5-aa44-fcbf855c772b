{"rules": [{"name": "start_all_services", "description": "启动所有服务", "content": "# 启动全部服务\n\n按照以下顺序依次启动所有服务：\n\n## 1. 基础服务\n\n### 系统服务\n```bash\ncd tx-cloud-base/tx-module-system/tx-module-system-api && mvn clean install -DskipTests\ncd tx-cloud-base/tx-module-system/tx-module-system-biz && mvn spring-boot:run -Dspring.profiles.active=dev -Dspring.cloud.nacos.discovery.namespace=tangpanhui\n```\n\n### 基础设施服务\n```bash\ncd tx-cloud-base/tx-module-infra/tx-module-infra-api && mvn clean install -DskipTests\ncd tx-cloud-base/tx-module-infra/tx-module-infra-biz && mvn spring-boot:run -Dspring.profiles.active=dev -Dspring.cloud.nacos.discovery.namespace=tangpanhui\n```\n\n### 网关服务\n```bash\ncd tx-cloud-base/tx-gateway && mvn spring-boot:run -Dspring.profiles.active=dev -Dspring.cloud.nacos.discovery.namespace=tangpanhui\n```\n\n## 2. 业务服务\n\n### 会员服务\n```bash\ncd tx-module-member/tx-module-member-api && mvn clean install -DskipTests\ncd tx-module-member/tx-module-member-biz && mvn spring-boot:run -Dspring.profiles.active=dev -Dspring.cloud.nacos.discovery.namespace=tangpanhui\n```\n\n### CRM服务\n```bash\ncd tx-module-crm/tx-module-crm-api && mvn clean install -DskipTests\ncd tx-module-crm/tx-module-crm-biz && mvn spring-boot:run -Dspring.profiles.active=dev -Dspring.cloud.nacos.discovery.namespace=tangpanhui\n```\n\n### 报表服务\n```bash\ncd tx-module-report/tx-module-report-api && mvn clean install -DskipTests\ncd tx-module-report/tx-module-report-biz && mvn spring-boot:run -Dspring.profiles.active=dev -Dspring.cloud.nacos.discovery.namespace=tangpanhui\n```"}, {"name": "start_gateway", "description": "启动网关服务", "content": "# 启动网关服务\n\n```bash\ncd tx-cloud-base/tx-gateway && mvn spring-boot:run -Dspring.profiles.active=dev -Dspring.cloud.nacos.discovery.namespace=tangpanhui\n```\n\n网关服务端口：48080"}, {"name": "start_report", "description": "启动报表服务", "content": "# 启动报表服务\n\n```bash\ncd tx-module-report/tx-module-report-api && mvn clean install -DskipTests\ncd tx-module-report/tx-module-report-biz && mvn spring-boot:run -Dspring.profiles.active=dev -Dspring.cloud.nacos.discovery.namespace=tangpanhui\n```\n\n报表服务端口：48084"}, {"name": "start_crm", "description": "启动CRM服务", "content": "# 启动CRM服务\n\n```bash\ncd tx-module-crm/tx-module-crm-api && mvn clean install -DskipTests\ncd tx-module-crm/tx-module-crm-biz && mvn spring-boot:run -Dspring.profiles.active=dev -Dspring.cloud.nacos.discovery.namespace=tangpanhui\n```\n\nCRM服务端口：48085"}, {"name": "start_system", "description": "启动系统服务", "content": "# 启动系统服务\n\n```bash\ncd tx-cloud-base/tx-module-system/tx-module-system-api && mvn clean install -DskipTests\ncd tx-cloud-base/tx-module-system/tx-module-system-biz && mvn spring-boot:run -Dspring.profiles.active=dev -Dspring.cloud.nacos.discovery.namespace=tangpanhui\n```\n\n系统服务端口：48081"}, {"name": "start_infra", "description": "启动基础设施服务", "content": "# 启动基础设施服务\n\n```bash\ncd tx-cloud-base/tx-module-infra/tx-module-infra-api && mvn clean install -DskipTests\ncd tx-cloud-base/tx-module-infra/tx-module-infra-biz && mvn spring-boot:run -Dspring.profiles.active=dev -Dspring.cloud.nacos.discovery.namespace=tangpanhui\n```\n\n基础设施服务端口：48082"}, {"name": "start_member", "description": "启动会员服务", "content": "# 启动会员服务\n\n```bash\ncd tx-module-member/tx-module-member-api && mvn clean install -DskipTests\ncd tx-module-member/tx-module-member-biz && mvn spring-boot:run -Dspring.profiles.active=dev -Dspring.cloud.nacos.discovery.namespace=tangpanhui\n```\n\n会员服务端口：48083"}, {"name": "stop_services", "description": "关闭服务", "content": "# 关闭服务\n\n在运行服务的终端窗口中按 `Ctrl + C`，然后根据提示输入 `Y` 确认关闭。\n\n如果是通过VS Code调试面板启动的服务，可以点击红色的停止按钮（方形图标）停止服务。\n\n或者使用以下命令一次性关闭所有Java进程：\n```bash\ntaskkill /F /IM java.exe\n```"}, {"name": "restart_all_services", "description": "重启所有服务", "content": "# 重启所有服务\n\n```bash\nscripts\\restart_all_services.bat\n```\n\n这将会：\n1. 停止所有正在运行的Java进程\n2. 等待3秒确保所有进程都已停止\n3. 按顺序依次启动所有服务（系统服务 → 基础设施服务 → 网关服务 → 会员服务 → CRM服务 → 报表服务）\n4. 每个服务会在单独的终端窗口中启动"}, {"name": "restart_system", "description": "重启系统服务", "content": "# 重启系统服务\n\n```bash\nscripts\\restart_system.bat\n```\n\n这将会：\n1. 查找并停止占用48081端口的进程（系统服务）\n2. 等待2秒确保进程已停止\n3. 在新的终端窗口中启动系统服务"}, {"name": "restart_infra", "description": "重启基础设施服务", "content": "# 重启基础设施服务\n\n```bash\nscripts\\restart_infra.bat\n```\n\n这将会：\n1. 查找并停止占用48082端口的进程（基础设施服务）\n2. 等待2秒确保进程已停止\n3. 在新的终端窗口中启动基础设施服务"}, {"name": "restart_gateway", "description": "重启网关服务", "content": "# 重启网关服务\n\n```bash\nscripts\\restart_gateway.bat\n```\n\n这将会：\n1. 查找并停止占用48080端口的进程（网关服务）\n2. 等待2秒确保进程已停止\n3. 在新的终端窗口中启动网关服务"}, {"name": "restart_member", "description": "重启会员服务", "content": "# 重启会员服务\n\n```bash\nscripts\\restart_member.bat\n```\n\n这将会：\n1. 查找并停止占用48083端口的进程（会员服务）\n2. 等待2秒确保进程已停止\n3. 在新的终端窗口中启动会员服务"}, {"name": "restart_crm", "description": "重启CRM服务", "content": "# 重启CRM服务\n\n```bash\nscripts\\restart_crm.bat\n```\n\n这将会：\n1. 查找并停止占用48085端口的进程（CRM服务）\n2. 等待2秒确保进程已停止\n3. 在新的终端窗口中启动CRM服务"}, {"name": "restart_report", "description": "重启报表服务", "content": "# 重启报表服务\n\n```bash\nscripts\\restart_report.bat\n```\n\n这将会：\n1. 查找并停止占用48084端口的进程（报表服务）\n2. 等待2秒确保进程已停止\n3. 在新的终端窗口中启动报表服务"}, {"name": "service_management", "description": "服务管理指南", "content": "# 服务管理指南\n\n## 服务端口列表\n\n| 服务名称 | 端口 | 路径 |\n|---------|------|------|\n| 系统服务 | 48081 | tx-cloud-base/tx-module-system/tx-module-system-biz |\n| 基础设施服务 | 48082 | tx-cloud-base/tx-module-infra/tx-module-infra-biz |\n| 网关服务 | 48080 | tx-cloud-base/tx-gateway |\n| 会员服务 | 48087 | tx-module-member/tx-module-member-biz |\n| CRM服务 | 48085 | tx-module-crm/tx-module-crm-biz |\n| 报表服务 | 48084 | tx-module-report/tx-module-report-biz |\n\n## 启动服务\n\n### 启动单个服务\n使用VS Code任务面板选择对应的启动任务，例如：`启动系统服务`、`启动网关服务`等。\n\n### 启动所有服务\n使用VS Code任务面板选择`启动全部服务`任务，将按照正确的依赖顺序启动所有服务。\n\n## 停止服务\n\n### 停止单个服务\n在服务运行的终端窗口中按下`Ctrl+C`，然后输入`Y`确认停止。\n\n### 停止所有服务\n使用以下命令停止所有Java进程：\n```bash\ntaskkill /F /IM java.exe\n```\n\n## 重启服务\n\n### 重启单个服务\n使用VS Code任务面板选择对应的重启任务，例如：`重启系统服务`、`重启网关服务`等。\n\n### 重启所有服务\n使用VS Code任务面板选择`重启全部服务`任务。\n\n## 服务启动参数\n\n所有服务使用以下通用参数：\n- 环境配置：`-Dspring.profiles.active=dev`\n- Nacos命名空间：`-Dspring.cloud.nacos.discovery.namespace=tangpanhui`\n\n## 启动顺序\n\n正确的服务启动顺序为：\n1. 系统服务\n2. 基础设施服务\n3. 网关服务\n4. 会员服务\n5. CRM服务\n6. 报表服务"}]}