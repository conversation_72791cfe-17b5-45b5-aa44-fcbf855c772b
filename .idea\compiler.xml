<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
      </profile>
      <profile name="Annotation profile for tx-module-report-biz" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <processorPath useClasspath="false">
          <entry name="$PROJECT_DIR$/../../apache-maven-3.6.3/repository/org/projectlombok/lombok/1.18.36/lombok-1.18.36.jar" />
          <entry name="$PROJECT_DIR$/../../apache-maven-3.6.3/repository/org/mapstruct/mapstruct-processor/1.6.3/mapstruct-processor-1.6.3.jar" />
          <entry name="$PROJECT_DIR$/../../apache-maven-3.6.3/repository/org/mapstruct/mapstruct/1.6.3/mapstruct-1.6.3.jar" />
        </processorPath>
        <module name="tx-module-report-biz" />
      </profile>
      <profile name="Annotation profile for tx-module-report" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <processorPath useClasspath="false">
          <entry name="$PROJECT_DIR$/../../apache-maven-3.6.3/repository/org/springframework/boot/spring-boot-configuration-processor/3.4.1/spring-boot-configuration-processor-3.4.1.jar" />
          <entry name="$PROJECT_DIR$/../../apache-maven-3.6.3/repository/org/projectlombok/lombok/1.18.36/lombok-1.18.36.jar" />
          <entry name="$PROJECT_DIR$/../../apache-maven-3.6.3/repository/org/mapstruct/mapstruct-processor/1.6.3/mapstruct-processor-1.6.3.jar" />
          <entry name="$PROJECT_DIR$/../../apache-maven-3.6.3/repository/org/mapstruct/mapstruct/1.6.3/mapstruct-1.6.3.jar" />
        </processorPath>
        <module name="tx-spring-boot-starter-excel" />
        <module name="tx-spring-boot-starter-monitor" />
        <module name="tx-module-infra-biz" />
        <module name="tx-gateway" />
        <module name="tx-common" />
        <module name="tx-module-member-biz" />
        <module name="tx-module-member-api" />
        <module name="tx-spring-boot-starter-security" />
        <module name="tx-spring-boot-starter-redis" />
        <module name="tx-spring-boot-starter-web" />
        <module name="tx-module-system-biz" />
        <module name="tx-spring-boot-starter-biz-ip" />
        <module name="tx-module-report-api" />
        <module name="tx-spring-boot-starter-mybatis" />
        <module name="tx-spring-boot-starter-protection" />
        <module name="tx-module-crm-biz" />
        <module name="tx-spring-boot-starter-minio" />
        <module name="tx-spring-boot-starter-biz-data-permission" />
        <module name="tx-spring-boot-starter-mq" />
        <module name="tx-module-infra-api" />
        <module name="tx-spring-boot-starter-rpc" />
        <module name="tx-spring-boot-starter-biz-tenant" />
        <module name="tx-spring-boot-starter-test" />
        <module name="tx-spring-boot-starter-job" />
        <module name="tx-module-crm-api" />
        <module name="tx-module-system-api" />
        <module name="tx-spring-boot-starter-websocket" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="tx-spring-boot-starter-env" target="1.8" />
    </bytecodeTargetLevel>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="tx-common" options="-parameters" />
      <module name="tx-gateway" options="-parameters" />
      <module name="tx-module-crm" options="-parameters" />
      <module name="tx-module-crm-api" options="-parameters" />
      <module name="tx-module-crm-biz" options="-parameters" />
      <module name="tx-module-infra-api" options="-parameters" />
      <module name="tx-module-infra-biz" options="-parameters" />
      <module name="tx-module-member-api" options="-parameters" />
      <module name="tx-module-member-biz" options="-parameters" />
      <module name="tx-module-report-api" options="-parameters" />
      <module name="tx-module-report-biz" options="-parameters" />
      <module name="tx-module-system-api" options="-parameters" />
      <module name="tx-module-system-biz" options="-parameters" />
      <module name="tx-spring-boot-starter-biz-data-permission" options="-parameters" />
      <module name="tx-spring-boot-starter-biz-ip" options="-parameters" />
      <module name="tx-spring-boot-starter-biz-tenant" options="-parameters" />
      <module name="tx-spring-boot-starter-env" options="-parameters" />
      <module name="tx-spring-boot-starter-excel" options="-parameters" />
      <module name="tx-spring-boot-starter-job" options="-parameters" />
      <module name="tx-spring-boot-starter-minio" options="-parameters" />
      <module name="tx-spring-boot-starter-monitor" options="-parameters" />
      <module name="tx-spring-boot-starter-mq" options="-parameters" />
      <module name="tx-spring-boot-starter-mybatis" options="-parameters" />
      <module name="tx-spring-boot-starter-protection" options="-parameters" />
      <module name="tx-spring-boot-starter-redis" options="-parameters" />
      <module name="tx-spring-boot-starter-rpc" options="-parameters" />
      <module name="tx-spring-boot-starter-security" options="-parameters" />
      <module name="tx-spring-boot-starter-test" options="-parameters" />
      <module name="tx-spring-boot-starter-web" options="-parameters" />
      <module name="tx-spring-boot-starter-websocket" options="-parameters" />
    </option>
  </component>
</project>