<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>tx-cloud-base</artifactId>
        <groupId>cn.tianxing.cloud</groupId>
        <version>${revision}</version>
    </parent>
    <packaging>pom</packaging>
    <modules>
        <module>tx-common</module>
        <module>tx-spring-boot-starter-env</module>
        <module>tx-spring-boot-starter-mybatis</module>
        <module>tx-spring-boot-starter-redis</module>
        <module>tx-spring-boot-starter-web</module>
        <module>tx-spring-boot-starter-security</module>
        <module>tx-spring-boot-starter-websocket</module>
        <module>tx-spring-boot-starter-minio</module>
        <module>tx-spring-boot-starter-monitor</module>
        <module>tx-spring-boot-starter-protection</module>
<!--        <module>yudao-spring-boot-starter-config</module>-->
        <module>tx-spring-boot-starter-job</module>
        <module>tx-spring-boot-starter-mq</module>
        <module>tx-spring-boot-starter-rpc</module>

        <module>tx-spring-boot-starter-excel</module>
        <module>tx-spring-boot-starter-test</module>

        <module>tx-spring-boot-starter-biz-tenant</module>
        <module>tx-spring-boot-starter-biz-data-permission</module>
        <module>tx-spring-boot-starter-biz-ip</module>
    </modules>

    <artifactId>tx-framework</artifactId>
    <description>
        该包是技术组件，每个子包，代表一个组件。每个组件包括两部分：
            1. core 包：是该组件的核心封装
            2. config 包：是该组件基于 Spring 的配置

        技术组件，也分成两类：
            1. 框架组件：和我们熟悉的 MyBatis、Redis 等等的拓展
            2. 业务组件：和业务相关的组件的封装，例如说数据字典、操作日志等等。
        如果是业务组件，Maven 名字会包含 biz
    </description>
    <url>https://github.com/YunaiV/ruoyi-vue-pro</url>

</project>
