{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "build",
            "type": "shell",
            "command": "mvn",
            "args": [
                "clean",
                "install",
                "-DskipTests"
            ],
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "problemMatcher": []
        },
        {
            "label": "启动system服务",
            "type": "shell",
            "command": "${workspaceFolder}/scripts/start_system.bat",
            "presentation": {
                "reveal": "always",
                "panel": "new",
                "clear": true
        },
            "problemMatcher": []
        },
        {
            "label": "启动infra服务",
            "type": "shell",
            "command": "${workspaceFolder}/scripts/start_infra.bat",
            "presentation": {
                "reveal": "always",
                "panel": "new",
                "clear": true
            },
            "problemMatcher": []
        },
        {
            "label": "启动gateway服务",
            "type": "shell",
            "command": "${workspaceFolder}/scripts/start_gateway.bat",
            "presentation": {
                "reveal": "always",
                "panel": "new",
                "clear": true
            },
            "problemMatcher": []
        },
        {
            "label": "启动member服务",
            "type": "shell",
            "command": "${workspaceFolder}/scripts/start_member.bat",
            "presentation": {
                "reveal": "always",
                "panel": "new",
                "clear": true
            },
            "problemMatcher": []
        },
        {
            "label": "启动crm服务",
            "type": "shell",
            "command": "${workspaceFolder}/scripts/start_crm.bat",
            "presentation": {
                "reveal": "always",
                "panel": "new",
                "clear": true,
            },
            "problemMatcher": []
        },
        {
            "label": "启动report服务",
            "type": "shell",
            "command": "${workspaceFolder}/scripts/start_report.bat",
            "presentation": {
                "reveal": "always",
                "panel": "new",
                "clear": true,
            },
            "problemMatcher": []
        },
        {
            "label": "启动all服务",
            "dependsOrder": "parallel",
            "dependsOn": [
                "启动system服务",
                "启动infra服务",
                "启动gateway服务",
                "启动member服务",
                "启动crm服务",
                "启动report服务"
            ]
       }
    ]
} 