[{"contentPath": "java/InfraCategoryListReqVO", "filePath": "yudao-module-infra/yudao-module-infra-biz/src/main/java/cn/iocoder/yudao/module/infra/controller/admin/demo/vo/InfraCategoryListReqVO.java"}, {"contentPath": "java/InfraCategoryRespVO", "filePath": "yudao-module-infra/yudao-module-infra-biz/src/main/java/cn/iocoder/yudao/module/infra/controller/admin/demo/vo/InfraCategoryRespVO.java"}, {"contentPath": "java/InfraCategorySaveReqVO", "filePath": "yudao-module-infra/yudao-module-infra-biz/src/main/java/cn/iocoder/yudao/module/infra/controller/admin/demo/vo/InfraCategorySaveReqVO.java"}, {"contentPath": "java/InfraCategoryController", "filePath": "yudao-module-infra/yudao-module-infra-biz/src/main/java/cn/iocoder/yudao/module/infra/controller/admin/demo/InfraCategoryController.java"}, {"contentPath": "java/InfraCategoryDO", "filePath": "yudao-module-infra/yudao-module-infra-biz/src/main/java/cn/iocoder/yudao/module/infra/dal/dataobject/demo/InfraCategoryDO.java"}, {"contentPath": "java/InfraCategoryMapper", "filePath": "yudao-module-infra/yudao-module-infra-biz/src/main/java/cn/iocoder/yudao/module/infra/dal/mysql/demo/InfraCategoryMapper.java"}, {"contentPath": "xml/InfraCategoryMapper", "filePath": "yudao-module-infra/yudao-module-infra-biz/src/main/resources/mapper/demo/InfraCategoryMapper.xml"}, {"contentPath": "java/InfraCategoryServiceImpl", "filePath": "yudao-module-infra/yudao-module-infra-biz/src/main/java/cn/iocoder/yudao/module/infra/service/demo/InfraCategoryServiceImpl.java"}, {"contentPath": "java/InfraCategoryService", "filePath": "yudao-module-infra/yudao-module-infra-biz/src/main/java/cn/iocoder/yudao/module/infra/service/demo/InfraCategoryService.java"}, {"contentPath": "java/InfraCategoryServiceImplTest", "filePath": "yudao-module-infra/yudao-module-infra-biz/src/test/java/cn/iocoder/yudao/module/infra/service/demo/InfraCategoryServiceImplTest.java"}, {"contentPath": "java/ErrorCodeConstants_手动操作", "filePath": "yudao-module-infra/yudao-module-infra-api/src/main/java/cn/iocoder/yudao/module/infra/enums/ErrorCodeConstants_手动操作.java"}, {"contentPath": "sql/sql", "filePath": "sql/sql.sql"}, {"contentPath": "sql/h2", "filePath": "sql/h2.sql"}, {"contentPath": "vue/index", "filePath": "yudao-ui-admin-vue3/src/views/infra/demo/index.vue"}, {"contentPath": "vue/CategoryForm", "filePath": "yudao-ui-admin-vue3/src/views/infra/demo/CategoryForm.vue"}, {"contentPath": "ts/index", "filePath": "yudao-ui-admin-vue3/src/api/infra/demo/index.ts"}]