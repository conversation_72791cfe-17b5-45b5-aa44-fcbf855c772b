@echo off
chcp 65001 >nul
echo Starting CRM service...

cd /d "%~dp0.."
set "JAVA_OPTS=-Dspring.profiles.active=dev -Dspring.cloud.nacos.discovery.namespace=tangpanhui -Xms512m -Xmx1024m -Dfile.encoding=UTF-8 -Dsun.stdout.encoding=UTF-8 -Dsun.stderr.encoding=UTF-8 -Dlog4j.charset=UTF-8 -Dlog4j2.charset=UTF-8"

echo Compiling CRM module...
call mvn -f tx-module-crm/pom.xml clean install -DskipTests
echo CRM module compilation completed

call mvn -f tx-module-crm/tx-module-crm-biz/pom.xml spring-boot:run -Dspring-boot.run.jvmArguments="%JAVA_OPTS%"

echo CRM service started 